import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

/* Layout */
import Layout from '../views/layout/Layout';

/**
 * hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
 *                                if not set alwaysShow, only more than one route under the children
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirct in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    title: 'title'               the name show in submenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar,
  }
 **/
export const constantRouterMap = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  { path: '/404', component: () => import('@/views/404'), hidden: true },
  {
    path: '',
    component: Layout,
    redirect: '/home',
    meta: { title: '首页', icon: 'home', noCache: true, affix: true },
    children: [
      {
        path: 'home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: { title: '仪表盘', icon: 'dashboard', noCache: true, affix: true }
      },
      {
        path: 'changePassword',
        name: 'changePassword',
        component: () => import('@/views/changePassword.vue'),
        meta: { title: '修改密码' },
        hidden: true
      }
    ]
  },

  {
    path: '/pushAccount',
    component: Layout,
    redirect: '/pushAccount',
    meta: { title: '发布账号', icon: 'home', noCache: true, affix: true },
    hidden: true,
    children: [
      {
        path: 'pushAccount',
        name: 'pushAccount',
        component: () => import('@/views/pushAccount/pushAccount'),
        meta: { title: '发布账号', icon: 'dashboard'},
        hidden: true
      }

    ]
  }
];

export const asyncRouterMap = [
  {
    path: '/gameAccount',
    component: Layout,
    redirect: '/gameAccount/shouyougoods',
    name: 'gameAccount',
    meta: { title: '游戏账号管理', icon: ' product-attr' },
    children: [
      {
        path: 'shouyougoods',
        name: 'shouyougoods',
        component: () => import('@/views/gameAccount/shouyougoods/list'),
        meta: { title: '手游商品', icon: 'product-list' }
      }
      // {
      //   path: 'duanyougoods',
      //   name: 'duanyougoods',
      //   component: () => import('@/views/gameAccount/duanyougoods/list'),
      //   meta: { title: '端游商品', icon: 'product-list' }
      // },
    ]
  },
  {
    path: '/verifyCenter',
    name: 'verifyCenter',
    component: Layout,
    meta: { title: '审核中心', icon: 'product-list' },
    children: [
      // {
      //   path: 'accountVerify',
      //   name: 'accountVerify',
      //   component: () => import('@/views/verifyCenter/accountVerify/list'),
      //   meta: { title: '账号审核', icon: 'product-list' }
      // },
      {
        path: 'accountVerify',
        name: 'accountVerify',
        component: () => import('@/views/verifyCenterNew/accountVerify/list'),
        meta: { title: '账号审核', icon: 'product-list' }
      },
      {
        path: 'beopeiVerify',
        name: 'beopeiVerify',
        component: () => import('@/views/verifyCenter/beopeiVerify/list'),
        meta: { title: '包赔审核', icon: 'product-list' }
      },
      {
        path: 'verifyStats',
        name: 'verifyStats',
        component: () => import('@/views/verifyCenterNew/verifyStats/list'),
        meta: { title: '账号审核', icon: 'product-list' }
      },
      {
        path: 'productLogList',
        name: 'productLogList',
        component: () => import('@/views/productLogList/list'),
        meta: { title: '账号审核', icon: 'product-list' }
      }
    ]
  },
  {
    path: '/stats',
    name: 'stats',
    component: Layout,
    meta: { title: '统计中心', icon: 'product-list' },
    children: [
 {
        path: 'statsRecord',
        name: 'statsRecord',
        component: () => import('@/views/iframePage'),
        meta: { title: '录号统计', icon: 'user' }
      },
      {
        path: 'statsUser',
        name: 'statsUser',
        component: () => import('@/views/iframePage'),
        meta: { title: '用户统计', icon: 'user' }
      },
      {
        path: 'statsProduct',
        name: 'statsProduct',
        component: () => import('@/views/iframePage'),
        meta: { title: '商品统计', icon: 'user' }
      },
      {
        path: 'statsOrder',
        name: 'statsOrder',
        component: () => import('@/views/iframePage'),
        meta: { title: '订单统计', icon: 'user' }
      },
      {
        path: 'statsNegotiation',
        name: 'statsNegotiation',
        component: () => import('@/views/iframePage'),
        meta: { title: '议价统计', icon: 'user' }
      },
      {
        path: 'statsChat',
        name: 'statsChat',
        component: () => import('@/views/iframePage'),
        meta: { title: '会话统计', icon: 'user' }
      },
      {
        path: 'statsSearch',
        name: 'statsSearch',
        component: () => import('@/views/iframePage'),
        meta: { title: '搜索统计', icon: 'user' }
      },
      {
        path: 'statsBM',
        name: 'statsBM',
        component: () => import('@/views/iframePage'),
        meta: { title: '号商统计', icon: 'product' }
      },
      {
        path: 'statistic',
        name: 'statistic',
        component: () => import('@/views/statistic/index'),
        meta: { title: '看看-拉新报表', icon: 'product' }
      },
      {
        path: 'yokoyeStatistic',
        name: 'statisticy',
        component: () => import('@/views/statistic/yokoye'),
        meta: { title: '游可易-拉新报表', icon: 'product' }
      },
      {
        path: 'yokoyeReinforcementReport',
        name: 'yokoyeReinforcementReport',
        component: () => import('@/views/statistic/yokoye/reinforcementReport'),
        meta: { title: '游可易-拉新结算报表', icon: 'product' }
      },
      {
        path: 'yokoyeCustomerAcquisition',
        name: 'yokoyeCustomerAcquisition',
        component: () => import('@/views/statistic/yokoye/customerAcquisition'),
        meta: { title: '游可易-拉新获客情况报表', icon: 'product' }
      },
      {
        path: 'yokoyeFinancialDetails',
        name: 'yokoyeFinancialDetails',
        component: () => import('@/views/statistic/yokoye/financialDetails'),
        meta: { title: '游可易-财务明细报表', icon: 'product' }
      },
      {
        path: 'kkzhwFinancialDetails',
        name: 'kkzhwFinancialDetails',
        component: () => import('@/views/statistic/kkzhw/financialDetails'),
        meta: { title: '看看-财务明细报表', icon: 'product' }
      },
    ]
  },
  {
    path: '/luhao',
    name: 'luhao',
    component: Layout,
    meta: { title: '录号', icon: 'product-list' },
    children: [
      {
        path: 'luhaoManage',
        name: 'luhaoManage',
        component: () => import('@/views/luhao/luhaoManage/list'),
        meta: { title: '录号管理', icon: 'product-list' }
      },
      {
        path: 'taskManage',
        name: 'taskManage',
        component: () => import('@/views/luhao/taskManage/list'),
        meta: { title: '任务管理', icon: 'product-list' }
      }
    ]
  },
  {
    path: '/imteamManage',
    component: Layout,
    redirect: '/imteamManage/imteamManageList',
    name: 'imteamManage',
    meta: { title: '会话管理', icon: ' user' },
    children: [
      {
        path: 'imteamManageList',
        name: 'imteamManage_list',
        component: () => import('@/views/imteamManage/imteamManageList/index'),
        meta: { title: '会话列表', icon: 'user' }
      },
      {
        path: 'imteamManageStatics',
        name: 'imteamManage_statics',
        component: () =>
          import('@/views/imteamManage/imteamManageStatics/index'),
        meta: { title: '会话监控', icon: 'user' }
      },
      {
        path: 'memberTalkManageList',
        name: 'memberTalkManage_list',
        component: () =>
          import('@/views/imteamManage/memberTalkManageList/index'),
        meta: { title: '咨询会话列表', icon: 'user' }
      },
      {
        path: 'consultRecordsList',
        name: 'consultRecords_list',
        component: () =>
          import('@/views/imteamManage/consultRecordsList/index'),
        meta: { title: '服务小记列表', icon: 'user' }
      },
      {
        path: 'chatMonitor',
        name: 'chatMonitor',
        component: () => import('@/views/imteamManage/chatMonitor/index'),
        meta: { title: '会话质检', icon: 'user' }
      },
      {
        path: 'chatMonitorDetail',
        name: 'chatMonitorDetail',
        component: () => import('@/views/imteamManage/chatMonitor/detail'),
        meta: { title: '会话质检详情', icon: 'user' },
        hidden: true
      }
    ]
  },
  {
    path: '/memberCenter',
    component: Layout,
    redirect: '/memberCenter/memberProdcut',
    name: 'bigmember_center',
    meta: { title: '商家中心', icon: ' user' },
    children: [
      {
        path: 'memberProdcut',
        name: 'memberProdcut',
        component: () => import('@/views/memberCenter/memberProdcut/index'),
        meta: { title: '商品管理', icon: 'user' }
      },
      {
        path: 'recoveryProduct',
        name: 'recoveryProduct',
        component: () => import('@/views/memberCenter/recoveryProduct/index'),
        meta: { title: '回收池', icon: 'user' }
      },
      {
        path: 'memberOrderList',
        name: 'memberOrderList',
        component: () => import('@/views/memberCenter/memberOrderList/index'),
        meta: { title: '订单中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/productKF',
    component: Layout,
    redirect: '/productKF/imList',
    name: 'productKF',
    meta: { title: '客服工作台', icon: 'product' },
    children: [
      {
        path: 'imList',
        name: 'imlist',
        component: () => import('@/views/productKF/imlist/index'),
        meta: { title: '会话列表', icon: 'product', keepAlive: true }
      }
    ]
  },
   {
    path: '/answer',
    component: Layout,
    redirect: '/answer/answer',
    name: 'answer',
    meta: { title: '看看答题', icon: 'product' },
    children: [
      {
        path: 'answer',
        name: 'answer',
        component: () => import('@/views/answer/index'),
        meta: { title: '看看答题', icon: 'product', keepAlive: true }
      },
      {
        path: 'questions',
        name: 'answerQuestions',
        component: () => import('@/views/answer/questions'),
        meta: { title: '题目管理' },
        hidden: true
      },
      {
        path: 'question-form',
        name: 'answerQuestionForm',
        component: () => import('@/views/answer/question-form'),
        meta: { title: '题目表单' },
        hidden: true
      },
      {
        path: 'question-detail',
        name: 'answerQuestionDetail',
        component: () => import('@/views/answer/question-detail'),
        meta: { title: '题目详情' },
        hidden: true
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    redirect: '/config/list',
    name: 'config',
    meta: { title: '配置中心', icon: 'product' },
    children: [
      {
        path: 'list',
        name: 'configList',
        component: () => import('@/views/config/list'),
        meta: { title: '配置中心', icon: 'product', keepAlive: true }
      }
    ]
  },
  // {
  //   path: '/promotionChannels',
  //   component: Layout,
  //   redirect: '/promotionChannels/list',
  //   name: 'promotionChannels',
  //   meta: { title: '推广渠道', icon: 'product' },
  //   children: [
  //     {
  //       path: 'list',
  //       name: 'promotionChannelsList',
  //       component: () => import('@/views/promotionChannels/list'),
  //       meta: { title: '推广链接列表', icon: 'product', keepAlive: true }
  //     }
  //   ]
  // },
  {
    path: '/pms',
    component: Layout,
    redirect: '/pms/product',
    name: 'pms',
    meta: { title: '商品', icon: 'product' },
    children: [
      // {
      //   path: 'shouyougoodsList',
      //   name: 'shouyougoodsList',
      //   component: () => import('@/views/pms/shouyougoods/list'),
      //   meta: { title: '手游', icon: 'product-list' },
      //   hidden: true
      // },
      // {
      //   path: 'duanyougoodsList',
      //   name: 'duanyougoodsList',
      //   component: () => import('@/views/pms/duanyougoods/list'),
      //   meta: { title: '端游', icon: 'product-list' },
      //   hidden: true
      // },
      {
        path: 'product',
        name: 'product',
        component: () => import('@/views/pms/product/index'),
        meta: { title: '账号列表', icon: 'product-list' }
      },
      {
        path: 'addProduct',
        name: 'addProduct',
        component: () => import('@/views/pms/product/add'),
        meta: { title: '添加商品', icon: 'product-add' }
      },
      {
        path: 'updateProduct',
        name: 'updateProduct',
        component: () => import('@/views/pms/product/update'),
        meta: { title: '修改商品', icon: 'product-add' },
        hidden: true
      },
      {
        path: 'productCate',
        name: 'productCate',
        component: () => import('@/views/pms/productCate/index'),
        meta: { title: '商品分类', icon: 'product-cate' }
      },
      {
        path: 'productCateList',
        name: 'productCateList',
        component: () => import('@/views/pms/pardoctCateList/index'),
        meta: { title: '商品分类', icon: 'product-cate' }
      },
      {
        path: 'searchConfig',
        name: 'searchConfig',
        component: () => import('@/views/pms/pardoctCateList/searchConfig'),
        meta: { title: '快捷筛选配置' },
        hidden: true
      },
      {
        path: 'prefectureList',
        name: 'prefectureList',
        component: () => import('@/views/pms/pardoctCateList/prefectureList'),
        meta: { title: '专区管理' },
        hidden: true
      },
      {
        path: 'prefectureSearchTagList',
        name: 'prefectureSearchTagList',
        component: () => import('@/views/pms/pardoctCateList/prefectureSearchTagList'),
        meta: { title: '专区搜索标签管理' },
        hidden: true
      },
      {
        path: 'productCateSecond',
        name: 'productCateSecond',
        component: () => import('@/views/pms/productCate/second'),
        meta: { title: '商品分类', icon: 'product-cate' },
        hidden: true
      },
      {
        path: 'addProductCate',
        name: 'addProductCate',
        component: () => import('@/views/pms/productCate/add'),
        meta: { title: '添加商品分类' },
        hidden: true
      },
      {
        path: 'updateProductCate',
        name: 'updateProductCate',
        component: () => import('@/views/pms/productCate/update'),
        meta: { title: '修改商品分类' },
        hidden: true
      },
      {
        path: 'updateProductCateList',
        name: 'updateProductCateList',
        component: () => import('@/views/pms/pardoctCateList/update'),
        meta: { title: '修改商品分类' },
        hidden: true
      },
      {
        path: 'productAttr',
        name: 'productAttr',
        component: () => import('@/views/pms/productAttr/index'),
        meta: { title: '游戏SKU', icon: 'product-attr' }
      },
      {
        path: 'productAttrType',
        name: 'productAttrType',
        component: () => import('@/views/pms/productAttrType/index'),
        meta: { title: '游戏SKU', icon: 'product-attr' }
      },
      {
        path: 'productAttrList',
        name: 'productAttrList',
        component: () => import('@/views/pms/productAttr/productAttrList'),
        meta: { title: '商品属性列表' },
        hidden: true
      },
      {
        path: 'productAttrTypeList',
        name: 'productAttrTypeList',
        component: () => import('@/views/pms/productAttrType/productAttrList'),
        meta: { title: '商品属性列表' },
        hidden: true
      },
      {
        path: 'addProductAttrType',
        name: 'addProductAttrType',
        component: () => import('@/views/pms/productAttrType/addProductAttr'),
        meta: { title: '添加商品属性' },
        hidden: true
      },
      {
        path: 'updateProductAttrType',
        name: 'updateProductAttrType',
        component: () => import('@/views/pms/productAttrType/updateProductAttr'),
        meta: { title: '修改商品属性' },
        hidden: true
      },
      {
        path: 'addProductAttr',
        name: 'addProductAttr',
        component: () => import('@/views/pms/productAttr/addProductAttr'),
        meta: { title: '添加商品属性' },
        hidden: true
      },
      {
        path: 'updateProductAttr',
        name: 'updateProductAttr',
        component: () => import('@/views/pms/productAttr/updateProductAttr'),
        meta: { title: '修改商品属性' },
        hidden: true
      },
      {
        path: 'brand',
        name: 'brand',
        component: () => import('@/views/pms/brand/index'),
        meta: { title: '品牌管理', icon: 'product-brand' }
      },
      {
        path: 'addBrand',
        name: 'addBrand',
        component: () => import('@/views/pms/brand/add'),
        meta: { title: '添加品牌' },
        hidden: true
      },
      {
        path: 'updateBrand',
        name: 'updateBrand',
        component: () => import('@/views/pms/brand/update'),
        meta: { title: '编辑品牌' },
        hidden: true
      }
    ]
  },
  {
    path: '/oms',
    component: Layout,
    redirect: '/oms/order',
    name: 'oms',
    meta: { title: '订单', icon: 'order' },
    children: [
      {
        path: 'order',
        name: 'orderKK',
        component: () => import('@/views/oms/order/index'),
        meta: { title: '订单列表', icon: 'product-list' }
      },
      {
        path: 'orderYKY',
        name: 'orderYKY',
        component: () => import('@/views/oms/order/indexyky'),
        meta: { title: '订单列表', icon: 'product-list' }
      },
      {
        path: 'negoOrderList',
        name: 'negoOrderList',
        component: () => import('@/views/oms/order/negoOrderList'),
        meta: { title: '议价订单列表', icon: 'product-list' }
      },
      {
        path: 'collectionAccounts',
        name: 'collectionAccounts',
        component: () => import('@/views/oms/order/collectionAccounts'),
        meta: { title: '收款账户', icon: 'product-list' }
      },
      {
        path: 'orderDetail',
        name: 'orderDetail',
        component: () => import('@/views/oms/order/orderDetail'),
        meta: { title: '订单详情' },
        hidden: true
      },
      {
        path: 'productSH',
        name: 'merchantAccount',
        component: () => import('@/views/oms/order/productSH'),
        meta: { title: '商户列表', icon: 'product-list' }
      },
      {
        path: 'productHK',
        name: 'kkTransactionsKK',
        component: () => import('@/views/oms/order/productHK'),
        meta: { title: '汇款列表', icon: 'product-list' }
      },
      {
        path: 'kkTransactionsYKY',
        name: 'kkTransactionsYKY',
        component: () => import('@/views/oms/order/productHKYky'),
        meta: { title: '汇款列表', icon: 'product-list' }
      },
      {
        path: 'deliverOrderList',
        name: 'deliverOrderList',
        component: () => import('@/views/oms/order/deliverOrderList'),
        meta: { title: '发货列表' },
        hidden: true
      },
      {
        path: 'orderSetting',
        name: 'orderSetting',
        component: () => import('@/views/oms/order/setting'),
        meta: { title: '订单设置', icon: 'order-setting' }
      },
      {
        path: 'returnApply',
        name: 'returnApplyKK',
        component: () => import('@/views/oms/apply/index'),
        meta: { title: '退货申请处理-看看', icon: 'order-return' }
      },
      {
        path: 'returnApplyYKY',
        name: 'returnApplyYKY',
        component: () => import('@/views/oms/apply/returnApplyYKY'),
        meta: { title: '退货申请处理-游可易', icon: 'order-return' }
      },
      {
        path: 'returnReason',
        name: 'returnReason',
        component: () => import('@/views/oms/apply/reason'),
        meta: { title: '退货原因设置', icon: 'order-return-reason' }
      },
      {
        path: 'returnApplyDetail',
        name: 'returnApplyDetail',
        component: () => import('@/views/oms/apply/applyDetail'),
        meta: { title: '退货原因详情' },
        hidden: true
      },
      {
        path: 'pickOrder',
        name: 'pickOrder',
        component: () => import('@/views/oms/order/pickOrder'),
        meta: { title: '取色订单列表' },
        hidden: true
      },
      {
        path: 'negoList',
        name: 'negoList',
        component: () => import('@/views/oms/negoList/list'),
        meta: { title: '议价列表' },
        hidden: true
      },
      {
        path: 'contractList',
        name: 'contractListKK',
        component: () => import('@/views/oms/contractList/list'),
        meta: { title: '合同列表' },
        hidden: true
      },
      {
        path: 'contractListYKY',
        name: 'contractListYKY',
        component: () => import('@/views/oms/contractList/listyky.vue'),
        meta: { title: '合同列表' },
        hidden: true
      }
    ]
  },
  {
    path: '/sms',
    component: Layout,
    redirect: '/sms/coupon',
    name: 'sms',
    meta: { title: '营销', icon: 'sms' },
    children: [
      {
        path: 'homeCategory',
        name: 'homeCategory',
        component: () => import('@/views/sms/homeCategory/index'),
        meta: { title: '游戏推荐', icon: 'sms-flash' }
      },
      {
        path: 'flash',
        name: 'flash',
        component: () => import('@/views/sms/flash/index'),
        meta: { title: '秒杀活动列表', icon: 'sms-flash' }
      },
      {
        path: 'flashSession',
        name: 'flashSession',
        component: () => import('@/views/sms/flash/sessionList'),
        meta: { title: '秒杀时间段列表' },
        hidden: true
      },
      {
        path: 'selectSession',
        name: 'selectSession',
        component: () => import('@/views/sms/flash/selectSessionList'),
        meta: { title: '秒杀时间段选择' },
        hidden: true
      },
      {
        path: 'promotionChannelsList',
        name: 'promotionChannelsList',
        component: () => import('@/views/promotionChannels/list'),
        meta: { title: '推广链接列表', icon: 'product', keepAlive: true }
      },
      {
        path: 'flashProductRelation',
        name: 'flashProductRelation',
        component: () => import('@/views/sms/flash/productRelationList'),
        meta: { title: '秒杀商品列表' },
        hidden: true
      },
      {
        path: 'coupon',
        name: 'coupon',
        component: () => import('@/views/sms/coupon/index'),
        meta: { title: '优惠券列表', icon: 'sms-coupon' }
      },
      {
        path: 'couponRecord',
        name: 'couponRecord',
        component: () => import('@/views/sms/coupon/redemptionRecord'),
        meta: { title: '优惠券领取记录', icon: 'sms-coupon' }
      },
      {
        path: 'addCoupon',
        name: 'addCoupon',
        component: () => import('@/views/sms/coupon/add'),
        meta: { title: '添加优惠券' },
        hidden: true
      },
      {
        path: 'updateCoupon',
        name: 'updateCoupon',
        component: () => import('@/views/sms/coupon/update'),
        meta: { title: '修改优惠券' },
        hidden: true
      },
      {
        path: 'couponHistory',
        name: 'couponHistory',
        component: () => import('@/views/sms/coupon/history'),
        meta: { title: '优惠券领取详情' },
        hidden: true
      },
      {
        path: 'brand',
        name: 'homeBrand',
        component: () => import('@/views/sms/brand/index'),
        meta: { title: '品牌推荐', icon: 'product-brand' }
      },
      {
        path: 'new',
        name: 'homeNew',
        component: () => import('@/views/sms/new/index'),
        meta: { title: '新品推荐', icon: 'sms-new' }
      },
      {
        path: 'hot',
        name: 'homeHot',
        component: () => import('@/views/sms/hot/index'),
        meta: { title: '顶级推荐', icon: 'sms-hot' }
      },
      {
        path: 'subject',
        name: 'homeSubject',
        component: () => import('@/views/sms/subject/index'),
        meta: { title: '专题推荐', icon: 'sms-subject' }
      },
      {
        path: 'advertise',
        name: 'homeAdvertise',
        component: () => import('@/views/sms/advertise/index'),
        meta: { title: '广告列表', icon: 'sms-ad' }
      },
      {
        path: 'addAdvertise',
        name: 'addHomeAdvertise',
        component: () => import('@/views/sms/advertise/add'),
        meta: { title: '添加广告' },
        hidden: true
      },
      {
        path: 'updateAdvertise',
        name: 'updateHomeAdvertise',
        component: () => import('@/views/sms/advertise/update'),
        meta: { title: '编辑广告' },
        hidden: true
      }
    ]
  },
  {
    path: '/content',
    component: Layout,
    redirect: '/content/help',
    name: 'content',
    meta: { title: '内容管理', icon: 'product' },
    children: [
      {
        path: 'help',
        name: 'help',
        component: () => import('@/views/content/help/index'),
        meta: { title: '帮助中心', icon: 'help', brandId: 62 }
      },
      {
        path: 'huodong',
        name: 'huodong',
        component: () => import('@/views/content/help/index'),
        meta: { title: '帮助中心', icon: 'help', brandId: 125 }
      },
      {
        path: 'blackuser',
        name: 'blackuser',
        component: () => import('@/views/content/blackuser/index'),
        meta: { title: '黑号管理', icon: 'blackuser' }
      },
      {
        path: 'kefuguanli',
        name: 'kefuguanli',
        component: () => import('@/views/content/kefuguanli/index'),
        meta: { title: '客服管理', icon: 'product-list', id: 89 }
      },
      {
        path: 'innerSourceMgr',
        name: 'innerSourceMgr',
        component: () => import('@/views/content/kefuguanli/index'),
        meta: { title: '内部资源管理', icon: 'product-list', id: 107 }
      },
      {
        path: 'neirongguanli',
        name: 'neirongguanli',
        component: () => import('@/views/content/neirongguanli/index'),
        meta: { title: '主播管理', icon: 'product-list' }
      },
      {
        path: 'gameCharacters',
        name: 'gameCharacters',
        component: () => import('@/views/content/gameCharacters/index'),
        meta: { title: '十大高手', icon: 'blackuser' }
      },
      {
        path: 'memberReport',
        name: 'memberReport',
        component: () => import('@/views/content/memberReport/index'),
        meta: { title: '用户建议', icon: 'blackuser' }
      }
    ]
  },
  {
    path: '/memberManage',
    component: Layout,
    redirect: '/member/member',
    name: 'memberManage',
    meta: { title: '会员管理', icon: 'product' },
    children: [
      {
        path: 'member',
        name: 'member',
        component: () => import('@/views/memberManage/member/index'),
        meta: { title: '会员列表', icon: 'ums-member' }
      },
      {
        path: 'systemAccount',
        name: 'systemAccount',
        component: () => import('@/views/memberManage/systemAccount/index'),
        meta: { title: '会员列表', icon: 'ums-member' }
      },
      {
        path: 'address',
        name: 'address',
        component: () => import('@/views/memberManage/member/address'),
        meta: { title: '包赔资料', icon: 'ums-member' }
      },
      {
        path: 'bigMember',
        name: 'bigMember',
        component: () => import('@/views/memberManage/member/index'),
        meta: { title: '号商列表', icon: 'ums-member' }
      },
      {
        path: 'apiMember',
        name: 'apiMember',
        component: () => import('@/views/memberManage/member/apiIndex'),
        meta: { title: 'api号商', icon: 'ums-member' }
      }
    ]
  },
  {
    path: '/ums',
    component: Layout,
    redirect: '/ums/admin',
    name: 'ums',
    meta: { title: '权限', icon: 'ums' },
    children: [
      {
        path: 'admin',
        name: 'admin',
        component: () => import('@/views/ums/admin/index'),
        meta: { title: '用户列表', icon: 'ums-admin' }
      },
      {
        path: 'role',
        name: 'role',
        component: () => import('@/views/ums/role/index'),
        meta: { title: '角色列表', icon: 'ums-role' }
      },
      {
        path: 'allocMenu',
        name: 'allocMenu',
        component: () => import('@/views/ums/role/allocMenu'),
        meta: { title: '分配菜单' },
        hidden: true
      },
      {
        path: 'allocResource',
        name: 'allocResource',
        component: () => import('@/views/ums/role/allocResource'),
        meta: { title: '分配资源' },
        hidden: true
      },
      {
        path: 'menu',
        name: 'menu',
        component: () => import('@/views/ums/menu/index'),
        meta: { title: '菜单列表', icon: 'ums-menu' }
      },
      {
        path: 'addMenu',
        name: 'addMenu',
        component: () => import('@/views/ums/menu/add'),
        meta: { title: '添加菜单' },
        hidden: true
      },
      {
        path: 'updateMenu',
        name: 'updateMenu',
        component: () => import('@/views/ums/menu/update'),
        meta: { title: '修改菜单' },
        hidden: true
      },
      {
        path: 'resource',
        name: 'resource',
        component: () => import('@/views/ums/resource/index'),
        meta: { title: '资源列表', icon: 'ums-resource' }
      },
      {
        path: 'resourceCategory',
        name: 'resourceCategory',
        component: () => import('@/views/ums/resource/categoryList'),
        meta: { title: '资源分类' },
        hidden: true
      },
    ]
  },
  { path: '*', redirect: '/404', hidden: true }
];

export default new Router({
  // mode: 'history', //后端支持可开
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
});
