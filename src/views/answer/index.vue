<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container" shadow="never">
      <div style="margin-top: 15px">
        <el-form
          :inline="true"
          :model="listQuery"
          size="small"
          label-width="140px"
        >
          <el-form-item label="题目内容：">
            <el-input
              v-model="listQuery.keyword"
              class="input-width"
              placeholder="请输入题目内容"
              clearable
            />
          </el-form-item>
          <el-form-item label="游戏分类：">
            <el-input
              v-model="listQuery.gameCategory"
              class="input-width"
              placeholder="请输入游戏分类"
              clearable
            />
          </el-form-item>
          <el-form-item label="题目类型：">
            <el-select
              v-model="listQuery.questionType"
              placeholder="请选择题目类型"
              clearable
              style="width: 150px"
            >
              <el-option label="判断题" value="判断题" />
              <el-option label="选择题" value="选择题" />
              <el-option label="问答题" value="问答题" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearchList()">
              查询搜索
            </el-button>
            <el-button
              style="margin-left: 15px"
              size="small"
              @click="handleResetSearch()"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operate-container" shadow="never">
      <i class="el-icon-tickets" />
      <span>题目列表</span>
      <el-button
        size="mini"
        class="btn-add"
        @click="handleAddQuestion"
        style="float: right"
      >
        添加题目
      </el-button>
    </el-card>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="categoryTable"
        :data="list"
        style="width: 100%"
        border
      >
        <el-table-column label="ID" width="80" align="center">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>

        <el-table-column label="题目名称" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>

        <el-table-column label="题目类型" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.questionType }}</span>
          </template>
        </el-table-column>
        <el-table-column label="答案" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.correctAnswer }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              @change="handleStatusChange(scope.row)"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              inactive-color="#ff4949"
            >
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime | formatCreateTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="260" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleViewQuestion(scope.row)">
              查看
            </el-button>
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdateQuestion(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteQuestion(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        :current-page.sync="listQuery.pageNum"
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :total="total"
        background
        layout="total, sizes,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑题目对话框 -->
    <el-dialog
      :title="isEdit ? '编辑题目' : '添加题目'"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <el-form
        ref="questionForm"
        :model="questionForm"
        :rules="questionRules"
        label-width="120px"
      >
        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="questionForm.content"
            placeholder="请输入题目内容"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="题目类型" prop="questionType">
          <el-select
            v-model="questionForm.questionType"
            placeholder="请选择题目类型"
            style="width: 200px"
            @change="handleTypeChange"
          >
            <el-option label="判断题" value="判断题" />
            <el-option label="选择题" value="选择题" />
            <el-option label="问答题" value="问答题" />
          </el-select>
        </el-form-item>

        <el-form-item label="游戏分类" prop="gameCategory">
          <el-input
            v-model="questionForm.gameCategory"
            placeholder="请输入游戏分类"
          />
        </el-form-item>

        <!-- 选择题选项 -->
        <div v-if="questionForm.questionType === '选择题'">
          <el-form-item label="选项" prop="options">
            <div
              v-for="(_, index) in questionForm.options"
              :key="index"
              style="margin-bottom: 10px; display: flex; align-items: center"
            >
              <span style="margin-right: 10px; min-width: 20px"
                >{{ index + 1 }}.</span
              >
              <el-input
                v-model="questionForm.options[index]"
                :placeholder="`选项${index + 1}`"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                v-if="questionForm.options.length > 2"
                type="danger"
                icon="el-icon-delete"
                size="mini"
                @click="removeOption(index)"
              />
            </div>
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="addOption"
            >
              添加选项
            </el-button>
          </el-form-item>
        </div>

        <el-form-item label="正确答案" prop="correctAnswer">
          <!-- 判断题 -->
          <el-radio-group
            v-if="questionForm.questionType === '判断题'"
            v-model="questionForm.correctAnswer"
          >
            <el-radio label="是">是</el-radio>
            <el-radio label="否">否</el-radio>
          </el-radio-group>

          <!-- 选择题 -->
          <el-input
            v-else-if="questionForm.questionType === '选择题'"
            v-model="questionForm.correctAnswer"
            placeholder="请输入正确答案"
          />

          <!-- 问答题 -->
          <el-input
            v-else-if="questionForm.questionType === '问答题'"
            v-model="questionForm.correctAnswer"
            placeholder="请输入参考答案"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="答案解析" prop="answerAnalysis">
          <el-input
            v-model="questionForm.answerAnalysis"
            placeholder="请输入答案解析（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="题目配图" prop="imageUrl">
          <el-input
            v-model="questionForm.imageUrl"
            placeholder="请输入题目配图URL（可选）"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="questionForm.status"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchQuestionList,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  updateQuestionStatus,
  getGameCategories,
} from '@/api/answer';

const defaultListQuery = {
  pageNum: 1,
  pageSize: 20,
  keyword: '',
  questionType: '',
  gameCategory: '',
};

const defaultQuestionForm = {
  content: '',
  questionType: '',
  gameCategory: '',
  correctAnswer: '',
  answerAnalysis: '',
  imageUrl: '',
  options: ['', ''],
  status: true,
};

export default {
  name: 'AnswerIndex',
  data() {
    return {
      listLoading: false,
      confirmLoading: false,
      list: [],
      total: 0,
      listQuery: Object.assign({}, defaultListQuery),
      dialogVisible: false,
      isEdit: false,
      questionForm: Object.assign({}, defaultQuestionForm),
      questionRules: {
        content: [
          { required: true, message: '请输入题目内容', trigger: 'blur' },
          {
            min: 1,
            max: 500,
            message: '题目内容长度在1到500个字符',
            trigger: 'blur',
          },
        ],
        questionType: [
          { required: true, message: '请选择题目类型', trigger: 'change' },
        ],
        gameCategory: [
          { required: true, message: '请输入游戏分类', trigger: 'blur' },
        ],
        correctAnswer: [
          { required: true, message: '请输入正确答案', trigger: 'blur' },
        ],
      },
    };
  },

  filters: {
    formatCreateTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleString('zh-CN');
    },
  },

  created() {
    this.getList();
  },

  methods: {
    // 获取题目列表
    getList() {
      this.listLoading = true;
      fetchQuestionList(this.listQuery)
        .then((response) => {
          this.listLoading = false;
          if (response.data) {
            this.list = response.data.list || [];
            this.total = response.data.total || 0;
          }
        })
        .catch(() => {
          this.listLoading = false;
        });
    },

    // 搜索
    handleSearchList() {
      this.listQuery.pageNum = 1;
      this.getList();
    },

    // 重置搜索
    handleResetSearch() {
      this.listQuery = Object.assign({}, defaultListQuery);
      this.getList();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.listQuery.pageNum = 1;
      this.getList();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },

    // 添加题目
    handleAddQuestion() {
      this.isEdit = false;
      this.questionForm = Object.assign({}, defaultQuestionForm);
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.questionForm.clearValidate();
      });
    },

    // 编辑题目
    handleUpdateQuestion(row) {
      this.isEdit = true;
      this.questionForm = Object.assign({}, row);
      // 处理选择题选项
      if (row.questionType === '选择题') {
        if (row.options && Array.isArray(row.options)) {
          this.questionForm.options = [...row.options];
        } else if (typeof row.options === 'string') {
          // 如果是字符串格式，尝试解析
          try {
            this.questionForm.options = JSON.parse(row.options);
          } catch (e) {
            this.questionForm.options = ['', ''];
          }
        } else {
          this.questionForm.options = ['', ''];
        }
      } else {
        this.questionForm.options = [];
      }
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.questionForm.clearValidate();
      });
    },

    // 查看题目
    handleViewQuestion(row) {
      this.$message.info('查看题目: ' + row.content);
    },

    // 删除题目
    handleDeleteQuestion(row) {
      this.$confirm('确认要删除该题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteQuestion(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },

    // 状态开关改变
    handleStatusChange(row) {
      updateQuestionStatus(row.id, row.status)
        .then(() => {
          this.$message.success('状态更新成功');
        })
        .catch(() => {
          // 如果失败，恢复原状态
          row.status = !row.status;
          this.$message.error('状态更新失败');
        });
    },

    // 题目类型改变
    handleTypeChange() {
      // 清空答案相关字段
      this.questionForm.correctAnswer = '';

      // 如果是选择题，初始化选项列表
      if (this.questionForm.questionType === '选择题') {
        this.questionForm.options = ['', ''];
      } else {
        this.questionForm.options = [];
      }
    },

    // 添加选项
    addOption() {
      this.questionForm.options.push('');
    },

    // 删除选项
    removeOption(index) {
      this.questionForm.options.splice(index, 1);
    },

    // 确认添加/编辑
    handleConfirm() {
      this.$refs.questionForm.validate((valid) => {
        if (valid) {
          // 根据题目类型验证必填项
          if (this.questionForm.questionType === '选择题') {
            if (
              !this.questionForm.options ||
              this.questionForm.options.length < 2
            ) {
              this.$message.error('选择题至少需要填写两个选项');
              return;
            }

            // 检查是否有空选项
            const emptyOptions = this.questionForm.options.filter(
              (option) => !option || !option.trim()
            );
            if (emptyOptions.length > 0) {
              this.$message.error('选项内容不能为空');
              return;
            }
          }

          // 构建表单数据
          const formData = Object.assign({}, this.questionForm);
          formData.optionsList = formData.options;
          this.confirmLoading = true;
          if (this.isEdit) {
            // 编辑
            updateQuestion(this.questionForm.id, formData)
              .then(() => {
                this.confirmLoading = false;
                this.$message.success('编辑成功');
                this.dialogVisible = false;
                this.getList();
              })
              .catch(() => {
                this.confirmLoading = false;
              });
          } else {
            // 添加
            createQuestion(formData)
              .then(() => {
                this.confirmLoading = false;
                this.$message.success('添加成功');
                this.dialogVisible = false;
                this.getList();
              })
              .catch(() => {
                this.confirmLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.operate-container {
  margin-bottom: 20px;
  padding: 15px;
}

.operate-container .btn-add {
  margin-left: 10px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: center;
  padding: 20px 0;
}

.input-width {
  width: 200px;
}

.dialog-footer {
  text-align: right;
}
</style>