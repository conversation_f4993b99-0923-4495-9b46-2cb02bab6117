<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container" shadow="never">
      <div style="margin-top: 15px">
        <el-form
          :inline="true"
          :model="listQuery"
          size="small"
          label-width="140px"
        >
          <el-form-item label="分类名称：">
            <el-input
              v-model="listQuery.keyword"
              class="input-width"
              placeholder="请输入分类名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearchList()">
              查询搜索
            </el-button>
            <el-button
              style="margin-left: 15px"
              size="small"
              @click="handleResetSearch()"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operate-container" shadow="never">
      <i class="el-icon-tickets" />
      <span>答题分类列表</span>
      <el-button
        size="mini"
        class="btn-add"
        @click="handleAddCategory"
        style="float: right"
      >
        添加分类
      </el-button>
    </el-card>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="categoryTable"
        :data="list"
        style="width: 100%"
        border
      >
        <el-table-column label="ID" width="80" align="center">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>

        <el-table-column label="题目名称" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>

        <el-table-column label="题目类型" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.questionType }}</span>
          </template>
        </el-table-column>
        <el-table-column label="答案" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.correctAnswer }}</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime | formatCreateTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="260" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdateCategory(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteCategory(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        :current-page.sync="listQuery.pageNum"
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :total="total"
        background
        layout="total, sizes,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      :title="isEdit ? '编辑分类' : '添加分类'"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <el-form
        ref="categoryForm"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="categoryForm.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
} from '@/api/answer';

const defaultListQuery = {
  pageNum: 1,
  pageSize: 20,
  keyword: '',
};

const defaultCategoryForm = {
  name: '',
  sort: 0,
};

export default {
  name: 'AnswerIndex',
  data() {
    return {
      listLoading: false,
      confirmLoading: false,
      list: [],
      total: 0,
      listQuery: Object.assign({}, defaultListQuery),
      dialogVisible: false,
      isEdit: false,
      categoryForm: Object.assign({}, defaultCategoryForm),
      categoryRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '分类名称长度在1到50个字符',
            trigger: 'blur',
          },
        ],
        sort: [
          { required: true, message: '请输入排序值', trigger: 'blur' },
          { type: 'number', message: '排序值必须为数字', trigger: 'blur' },
        ],
      },
    };
  },

  filters: {
    formatCreateTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleString('zh-CN');
    },
  },

  created() {
    this.getList();
  },

  methods: {
    // 获取分类列表
    getList() {
      this.listLoading = true;
      fetchCategoryList(this.listQuery)
        .then((response) => {
          this.listLoading = false;
          if (response.data) {
            this.list = response.data.list || [];
            this.total = response.data.total || 0;
          }
        })
        .catch(() => {
          this.listLoading = false;
        });
    },

    // 搜索
    handleSearchList() {
      this.listQuery.pageNum = 1;
      this.getList();
    },

    // 重置搜索
    handleResetSearch() {
      this.listQuery = Object.assign({}, defaultListQuery);
      this.getList();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.listQuery.pageNum = 1;
      this.getList();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },

    // 添加分类
    handleAddCategory() {
      this.isEdit = false;
      this.categoryForm = Object.assign({}, defaultCategoryForm);
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.categoryForm.clearValidate();
      });
    },

    // 编辑分类
    handleUpdateCategory(row) {
      this.isEdit = true;
      this.categoryForm = Object.assign({}, row);
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.categoryForm.clearValidate();
      });
    },

    // 删除分类
    handleDeleteCategory(row) {
      this.$confirm('确认要删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteCategory(row.id).then(() => {
          this.$message.success('删除成功');
          this.getList();
        });
      });
    },

    // 查看题目
    handleViewQuestions(row) {
      this.$router.push({
        path: '/answer/questions',
        query: {
          categoryId: row.id,
          categoryName: row.name,
        },
      });
    },

    // 确认添加/编辑
    handleConfirm() {
      this.$refs.categoryForm.validate((valid) => {
        if (valid) {
          this.confirmLoading = true;
          if (this.isEdit) {
            // 编辑
            updateCategory(this.categoryForm.id, this.categoryForm)
              .then(() => {
                this.confirmLoading = false;
                this.$message.success('编辑成功');
                this.dialogVisible = false;
                this.getList();
              })
              .catch(() => {
                this.confirmLoading = false;
              });
          } else {
            // 添加
            createCategory(this.categoryForm)
              .then(() => {
                this.confirmLoading = false;
                this.$message.success('添加成功');
                this.dialogVisible = false;
                this.getList();
              })
              .catch(() => {
                this.confirmLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.operate-container {
  margin-bottom: 20px;
  padding: 15px;
}

.operate-container .btn-add {
  margin-left: 10px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: center;
  padding: 20px 0;
}

.input-width {
  width: 200px;
}

.dialog-footer {
  text-align: right;
}
</style>