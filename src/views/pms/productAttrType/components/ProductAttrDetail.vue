<template>
  <el-card class="form-container" shadow="never">
    <el-form
      ref="productAttrFrom"
      :model="productAttr"
      :rules="rules"
      label-width="150px"
    >
      <el-form-item v-if="hasRoles()" label="编辑类型:">
        <el-radio-group @change="radioChange" v-model="detailTtpe">
          <el-radio label="1">普通</el-radio>
          <el-radio v-if="hasRoles()" label="2">高级</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="属性类型：" prop="name">
        <el-select :disabled="detailTtpe == 1 ? true : false" v-model="type">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="属性名称：" prop="name">
        <el-input v-model="productAttr.name" />
      </el-form-item>
      <el-form-item label="属性分组：" prop="name">
        <el-input v-model="productAttr.nameGroup" />
      </el-form-item>

      <div v-if="detailTtpe == 2">
        <el-form-item prop="ename">
          <div slot="label">
            字段名称：
            <el-popover
              placement="top-start"
              title="（ename）搜索的时候配合sort 是32306使用"
              width="200"
              trigger="hover"
              content="如果是32306，有ename，放入queryStrParams里搜索。如果没有 ename，还是放在属性里搜索"
            >
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>
          <el-input v-model="productAttr.ename" />
        </el-form-item>
        <!-- <el-form-item label="商品类型：">
        <el-select
          v-model="productAttr.productAttributeCategoryId"
          placeholder="请选择"
        >
          <el-option
            v-for="item in productAttrCateList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
        <el-form-item>
          <div slot="label">
            属性筛选方式:
            <el-popover
              placement="top-start"
              title="(filterType)筛选条件"
              width="200"
              trigger="hover"
              content="筛选条件根据此属性决定是多选还是单选"
            >
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>
          <el-radio-group v-model="productAttr.filterType">
            <el-radio :label="0">单选</el-radio>
            <el-radio :label="1">多选</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <div slot="label">
            能否进行检索:
            <el-popover
              placement="top-start"
              title="(searchType)检索条件"
              width="200"
              trigger="hover"
              content="数值一般范围检索，高级检索会进入 PC 端的高级筛选"
            >
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>
          <el-radio-group v-model="productAttr.searchType">
            <el-radio :label="0">不需要检索</el-radio>
            <el-radio :label="1">关键字检索</el-radio>
            <el-radio :label="2">范围检索</el-radio>
            <el-radio :label="3">高级检索</el-radio>
            <el-radio :label="4">分组搜索</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <div slot="label">
            能否综合排序:
            <el-popover
              placement="top-start"
              title="排序条件"
              width="200"
              trigger="hover"
              content="H5 和 PC 根据此属性决定是否放入综合排序，建议 5 个以内，PC可能一行放不下太多"
            >
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>
          <el-select v-model="productAttr.searchSort" placeholder="请选择">
            <el-option
              v-for="item in searchSortList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品属性关联:">
          <el-radio-group v-model="productAttr.relatedStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <div slot="label">
            属性录入方式:
            <el-popover
              placement="top-start"
              title="(selectType)属性录入方式"
              width="200"
              trigger="hover"
              content="控制前端用户填写属性方式"
            >
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>

          <el-radio-group style="line-height:20px" v-model="productAttr.selectType">
            <el-radio :label="0">输入框</el-radio>
            <el-radio :label="1">单选</el-radio>
            <el-radio :label="2">复选</el-radio>
            <el-radio :label="3">级联</el-radio>
            <el-radio :label="4">叶子分组</el-radio>
            <el-radio :label="5">路径分组</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="属性值的录入方式:">
          <el-radio-group v-model="productAttr.inputType" disabled>
            <el-radio :label="0">手工录入</el-radio>
            <el-radio :label="1">从下面列表中选择</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <el-form-item label="属性值可选值列表:">
        <div v-show="productAttr.selectType === 3">
          <div id="jsoneditor" style="width: 500px; height: 400px"></div>
        </div>
        <el-input
          v-show="productAttr.selectType !== 3"
          :autosize="true"
          v-model="inputListFormat"
          type="textarea"
        />
      </el-form-item>
     <div>
      <el-form-item label="是否客户端必填:">
        <el-radio-group v-model="productAttr.handAddStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
          <el-radio :label="2">客户端隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="detailTtpe == 2" label="扩展字段">
        <div id="jsoneditor2" style="width: 900px; height: 400px"></div>
      </el-form-item>
      <el-form-item>
        <div slot="label">
          排序属性：
          <el-popover
            placement="top-start"
            title="（sort）前端排序使用"
            width="200"
            trigger="hover"
            content="商品上传，会根据属性类型 type 和 sort 排序决定输入优先级，另外 32306 代表 H5 页面单独筛选排第一个，20000 以上的代表 PC 要显示在顶部的属性"
          >
            <i slot="reference" class="el-icon-question"></i>
          </el-popover>
        </div>
        <el-input v-model="productAttr.sort" />
      </el-form-item>
     </div>
      <el-form-item>
        <el-button type="primary" @click="onSubmit('productAttrFrom')"
          >提交</el-button
        >
        <el-button v-if="!isEdit" @click="resetForm('productAttrFrom')"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import JSONEditor from 'jsoneditor';
import JSONEditorcss from 'jsoneditor/dist/jsoneditor.css';
import { fetchList } from '@/api/productAttrCate';
import {
  createProductAttr,
  getProductAttr,
  updateProductAttr,
} from '@/api/productAttr';
import { name } from 'file-loader';

const defaultProductAttr = {
  nameGroup: '',
  filterType: 0,
  handAddStatus: 0,
  inputList: '',
  inputType: 0,
  name: '',
  productAttributeCategoryId: 0,
  relatedStatus: 0,
  searchType: 0,
  searchSort: 0,
  selectType: 0,
  sort: 0,
  type: 0,
};
export default {
  name: 'ProductAttrDetail',
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      productAttrType:'',
      detailTtpe:'1',
      type: '',
      typeList: [
        {
          label: '扩展属性1',
          value: 1,
        },
        {
          label: '扩展属性2',
          value: 2,
        },
        {
          label: '扩展属性3',
          value: 3,
        },
        {
          label: '扩展属性4',
          value: 4,
        },
        {
          label: '扩展属性5',
          value: 5,
        },
        {
          label: '扩展属性6',
          value: 6,
        },
        {
          label: '扩展属性7',
          value: 7,
        },
      ],
      productAttr: Object.assign({}, defaultProductAttr),
      rules: {
        name: [
          { required: true, message: '请输入属性名称', trigger: 'blur' },
          {
            min: 1,
            max: 140,
            message: '长度在 1 到 140 个字符',
            trigger: 'blur',
          },
        ],
        ename: [
          {
            validator: (e, v, cb) => {
              this.validatorEname(e, v, cb);
            },
          },
        ],
      },
      productAttrCateList: null,
      inputListFormat: null,
    };
  },
  computed: {
    searchSortList() {
      const list = [
        {
          id: 0,
          name: '不排序',
        },
      ];
      for (let i = 1; i < 21; i++) {
        list.push({
          id: i,
          name: `排序${i}`,
        });
      }
      return list;
    },
  },
  watch: {
    inputListFormat: function (newValue, oldValue) {
      if (this.productAttr.selectType !== 3) {
        newValue = newValue.replace(/\n/g, ',');
      }
      this.productAttr.inputList = newValue;
    },
    'productAttr.selectType'(newValue, oldValue) {
      if (newValue === 0) {
        this.productAttr.inputType = 0;
      } else {
        this.productAttr.inputType = 1;
      }
    },
  },
  created() {},
  mounted() {
    this.initJsonEdit();
    this.initJsonEdit2();
    this.type = Number(this.$route.query.type);
    if (this.isEdit) {
      getProductAttr(this.$route.query.id).then((response) => {
        this.productAttr = response.data;
        this.productAttrType=response.data.type||''
        if (this.productAttr.custom) {
          this.setCustom2(this.productAttr.custom);
        }
        if (this.productAttr.selectType !== 3) {
          this.inputListFormat = this.productAttr.inputList.replace(/,/g, '\n');
        } else {
          this.inputListFormat = this.productAttr.inputList;
          this.setCustom(this.inputListFormat);
        }
      });
    } else {
      this.resetProductAttr();
    }
    this.getCateList();
  },
  methods: {
    hasRoles() {
      let roles = this.$store.getters.roles || [];
      let hasPower = roles.includes('超级管理员');
      
      return hasPower;
    },
    radioChange(e){
      console.log(this.productAttr,11111,this.productAttrType);
      if(e==1){
        this.type=this.productAttrType
      }
    },
    setCustom(custom) {
      let text = JSON.parse(custom);
      this.editor.set(text);
    },
    setCustom2(custom2) {
      let text = JSON.parse(custom2);
      this.editor2.set(text);
    },
    initJsonEdit() {
      var container = document.getElementById('jsoneditor');
      var options = {
        'mode': 'text',
        'indentation': 2,
        'enableSort': false,
        'enableTransform': false,
      };
      this.editor = new JSONEditor(container, options);
    },
    initJsonEdit2() {
      var container = document.getElementById('jsoneditor2');
      var options = {
        'mode': 'text',
        'indentation': 2,
        'enableSort': false,
        'enableTransform': false,
      };
      this.editor2 = new JSONEditor(container, options);
    },
    validatorEname(event, ename, callback) {
      if (ename) {
        const regex = /^[a-zA-Z0-9]+$/;
        if (!regex.test(ename)) {
          callback(new Error('只能输入字母和数字'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    getCateList() {
      const listQuery = { pageNum: 1, pageSize: 200 };
      fetchList(listQuery).then((response) => {
        this.productAttrCateList = response.data.list;
      });
    },
    resetProductAttr() {
      this.productAttr = Object.assign({}, defaultProductAttr);
      this.productAttr.productAttributeCategoryId = Number(
        this.$route.query.cid
      );
      this.type = Number(this.$route.query.type);
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirm('是否提交数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            if (this.productAttr.selectType === 3) {
              const jsonValue = this.editor.get();
              this.productAttr.inputList = JSON.stringify(jsonValue);
            }
         
            this.productAttr.type = this.type;
            let params={}
            if(this.detailTtpe==2){
              const jsonValue = this.editor2.get();
              this.productAttr.custom = JSON.stringify(jsonValue);
              params=this.productAttr
            }else{
              params={
                inputList:this.productAttr.inputList,
                type:this.productAttr.type,
                name:this.productAttr.name,
                nameGroup:this.productAttr.nameGroup,
              }
            }
            if (this.isEdit) {
              updateProductAttr(this.$route.query.id, params).then(
                (response) => {
                  if(response.code==200){
                  this.$message({
                    message: '修改成功',
                    type: 'success',
                    duration: 1000,
                  });
                  this.$router.back();
                }
                }
              );
            } else {
              createProductAttr(params).then((response) => {
                if(response.code==200){
                this.$message({
                  message: '提交成功',
                  type: 'success',
                  duration: 1000,
                });
                this.resetForm('productAttrFrom');
              }
              });
            }
          });
        } else {
          this.$message({
            message: '验证失败',
            type: 'error',
            duration: 1000,
          });
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.resetProductAttr();
    },
  },
};
</script>

<style scoped></style>
