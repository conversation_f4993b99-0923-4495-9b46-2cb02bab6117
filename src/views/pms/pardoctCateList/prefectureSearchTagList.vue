<template>
  <div>
    <el-button type="primary" class="add-help" @click="createConfig"
      >新增搜索标签</el-button
    >
    <el-table
      v-loading="listLoading"
      ref="productCateTable"
      :data="list"
      style="width: 100%"
      border
    >
      <el-table-column label="编号" width="100" align="center">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>
      <el-table-column label="标签名称" align="center">
        <template slot-scope="scope">{{ scope.row.tagName }}</template>
      </el-table-column>
      <el-table-column label="标签类型" align="center">
        <template slot-scope="scope">{{
          searchTagType(scope.row.tagType)
        }}</template>
      </el-table-column>
      <el-table-column label="是否生效" align="center">
        <template slot-scope="scope">{{
          scope.row.isActive ? '是' : '否'
        }}</template>
      </el-table-column>
      <el-table-column label="是否唯一" align="center">
        <template slot-scope="scope">{{
          scope.row.isUnique ? '是' : '否'
        }}</template>
      </el-table-column>

      <el-table-column label="标签说明" align="center">
        <template slot-scope="scope">{{ scope.row.description }}</template>
      </el-table-column>
      <el-table-column label="排序" width="100" align="center">
        <template slot-scope="scope">{{ scope.row.sort }}</template>
      </el-table-column>
      <!-- <el-table-column label="刷新索引" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  :disabled="
                    scope.row.level === 0 || (parentId != 73 && parentId != 74)
                  "
                  @click="refreshIndex(scope.$index, scope.row)"
                  >刷新索引
                </el-button>
              </template>
            </el-table-column> -->

      <el-table-column label="操作" width="260" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleConfigEdit(scope.$index, scope.row)"
            >搜索配置
          </el-button>
          <el-button size="mini" @click="handleUpdate(scope.$index, scope.row)"
            >编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.$index, scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="新增标签" width="600px" :visible.sync="tagVisible">
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="标签名称" prop="tagName">
            <el-input v-model="ruleForm.tagName"></el-input>
          </el-form-item>
          <el-form-item label="标签类型" prop="tagType">
            <el-radio-group v-model="ruleForm.tagType">
              <el-radio :label="0">h5</el-radio>
              <el-radio :label="1">pc</el-radio>

              <el-radio :label="2">h5和pc</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否生效">
            <el-radio-group v-model="ruleForm.isActive">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否唯一">
            <el-radio-group v-model="ruleForm.isUnique">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="排序">
            <el-input v-model="ruleForm.sort"></el-input>
          </el-form-item>
          <el-form-item label="标签说明">
            <el-input type="textarea" v-model="ruleForm.description"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="tagVisible = false">取 消</el-button>
        <el-button type="primary" @click="tagSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <SearchBox :detailSearchZoneTagApi="detailSearchZoneTag" :updateSearchZoneTagApi="updateSearchZoneTag" ref="searchRef"/>
  </div>
</template>

<script>
import {
  getProductAttribute,
  updateSearchTag,
  deleteSearchTag,
  detailSearchTag,
  searchProductList2,
  detailSearchZoneTag,
  getListByZone,
  addSearchZoneTag,
  deleteSearchZoneTag,
  updateSearchZoneTag
} from '@/api/searchConfig';
import CheckBoxList from './components/checkBoxList.vue';
import InputNumber from './components/ipuntNumber.vue';
import SearchBox from './components/index'
export default {
  components: {
    CheckBoxList,
    InputNumber,
    SearchBox
  },
  data() {
    return {
      list: null,
      total: null,
      listLoading: true,
      tabPosition: 'chat',
      dialogFormVisible: false,
      zoneTypeList: [
        {
          label: '首页专区',
          value: 0,
        },
        {
          label: '其他专区',
          value: 1,
        },
      ],
      getZoneTypeName: { '0': '首页专区','1': '其他专区' },
      ruleForm: {
        tagName: '',
        tagType: 0,
        description: '',
        sort: 0,
        isActive: 0,
        isUnique: 0,
      },
      rules: {
        name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
        tagType: [
          { required: true, message: '请选择标签类型', trigger: 'change' },
        ],
      },
      tagVisible: false,
      listQuery: {
        pageNum: 1,
        pageSize: 20,
      },
      parentId: 74,

      keyword2: '',
      keyword: '',
      productCategoryId: '', //分类ID
      tagId:'',
      checkBoxAttributeList: [],
      checkBoxAttrGroup: [],
      optsSearchResult: [],
      inputAttributeList: [],
      isExpand: false,
      tagDetail: {},
      searchTotal: '',
      isUniqueFlag: 0,
    };
  },
  computed: {
    newCheckBoxAttrGroup() {
      return this.checkBoxAttrGroup;
      //   return this.isExpand
      //     ? this.checkBoxAttrGroup
      //     : this.checkBoxAttrGroup.slice(0, 8);
    },
  },
  mounted() {
    this.productCategoryId = this.$route.query.productCategoryId
    this.tagId=this.$route.query.id
    this.getList();
    // this.initJsonEdit()
  },
  methods: {
    detailSearchZoneTag,
    updateSearchZoneTag,
    searchTagType(v) {
      let value = '';
      switch (v) {
        case 0:
          value = 'H5';
          break;
        case 1:
          value = 'PC';
          break;
        case 2:
          value = 'H5和PC';
          break;
        default:
          value = '';
          break;
      }
      return value;
    },
    getList() {
      this.listLoading = true;
      getListByZone(this.tagId, this.listQuery).then((res) => {
        this.listLoading = false;
        this.list = res.data.list;
        //   this.total = res.data.total;
      });
    },
    //标签新增提交和编辑提交
    tagSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.ruleForm,
            categoryId: this.productCategoryId,
            zoneId:this.tagId
          };
          if (params.id) {
            updateSearchZoneTag(params.id, params).then((res) => {
              if (res.code == 200) {
                this.$message.success('修改成功');
                this.tagVisible = false;
                this.getList();
              }
            });
          } else {
            addSearchZoneTag(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('新增成功');
                this.tagVisible = false;
                this.getList();
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    //删除
    handleDelete(index, row) {
      this.$confirm('是否要删除该标签', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteSearchZoneTag(row.id).then((res) => {
          if (res.code == 200) {
            this.$message.success('删除成功');
            this.getList();
          }
        });
      });
    },
    //编辑
    handleUpdate(index, row) {
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.tagVisible = true;
    },
    //新增
    createConfig() {
      (this.ruleForm = {
        tagName: '',
        tagType: 0,
        description: '',
        sort: 0,
        isActive: 0,
        isUnique: 0,
      }),
        (this.tagVisible = true);
    },
    //标签配置
    handleConfigEdit(index, row) {
        this.$refs.searchRef.handleConfigEdit(row)
    },
  },
};
</script>
<style scoped lang="scss">
.add-help {
  float: right;
  margin: 20px 20px 20px 0;
}
.opt-item {
  cursor: pointer;
  padding: 0px 22px;
  background: #f6f6f6;
  border-radius: 20px;
  margin-right: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
  line-height: 30px !important;
  height: 36px !important;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
  display: flex;
  align-items: center;

  &.active {
    background: #fff4ee;
    color: #ff6716;
    border-color: #fff4ee;
  }
}
.keyword_box {
  // margin: 0 0 20px 0;
  padding-bottom: 20px;
  .search_keyword {
    width: 265px;
    margin-right: 10px;

    /deep/ .el-input__inner {
      border-radius: 50px !important;
    }
  }
}

.flexWrap {
  flex-wrap: wrap;
}
.playSearch_tit {
  flex-shrink: 0;
  width: 110px;
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}
</style>
