<template>
  <div>
    <el-tabs v-model="tabValue" @tab-click="changeTab">
      <el-tab-pane label="基本信息" />
      <!-- <el-tab-pane label="营销信息" />
      <el-tab-pane label="用户信息" /> -->
    </el-tabs>

    <el-form
      ref="productForm"
      :model="value"
      :rules="rules"
      :label-width="formLabelWidth"
      class="form-box"
    >
      <div v-show="tabValue === '0'">
        <el-card class="card-box">
          <el-form-item
            v-show="value.productSn"
            label="商品编号："
            prop="productSn"
          >
            <el-input v-model="value.productSn" disabled />
          </el-form-item>
          <el-form-item label="游戏类型：" prop="productCategoryId">
            <el-cascader
              v-model="selectProductCateValue"
              :options="productCateOptions"
              disabled
            />
          </el-form-item>
          <el-form-item label="游戏品牌：" prop="brandId">
            <el-select
              v-model="value.brandId"
              placeholder="请选择品牌"
              @change="handleBrandChange"
            >
              <el-option
                v-for="item in brandOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="游戏SKU：">
            <el-select
              v-model="value.productAttributeCategoryId"
              placeholder="请选择属性类型"
              @change="handleProductAttrChange"
            >
              <el-option
                v-for="item in productAttributeCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="区服信息：" prop="gameAccountQufu">
            <el-input v-model="value.gameAccountQufu" disabled />
          </el-form-item>
          <!--扩展属性-->
          <div v-if="extList['ext1'].needShow" shadow="never" class="ext1">
            <tedian
              :detail-options="extList['ext1'].detailOptions"
              :opetion-date="extList['ext1'].opetionDate"
              @changequfu="changequfu"
            />
          </div>
        </el-card>

        <el-card class="card-box">
          <div class="card-title">商品规格</div>
          <el-form-item label="售价金额：" prop="price">
            <el-input v-model="value.price" />
          </el-form-item>
          <el-form-item label="心理底价：" prop="originalPrice">
            <el-input v-model="value.originalPrice" />
          </el-form-item>
          <!-- <el-form-item label="商品规格：" style="width:90%">
            <el-card shadow="never" class="cardBg">
              <div v-for="(productAttr, idx) in selectProductAttr" :key="idx">
                {{ productAttr.name }}：
                <el-checkbox-group
                  v-if="productAttr.handAddStatus === 0"
                  v-model="selectProductAttr[idx].values"
                  @change="selectProductAttrChange"
                >
                  <el-checkbox
                    v-for="item in getInputListArr(productAttr.inputList)"
                    :label="item"
                    :key="item"
                    class="littleMarginLeft"
                  />
                </el-checkbox-group>
              </div>
            </el-card>
            <el-table
              :data="value.skuStockList"
              style="width: 100%;margin-top: 20px"
              border
            >
              <el-table-column
                v-for="(item, index) in selectProductAttr"
                :label="item.name"
                :key="item.id"
                align="center"
              >
                <template slot-scope="scope">
                  {{ getProductSkuSp(scope.row, index) }}
                </template>
              </el-table-column>
              <el-table-column label="销售价格" width="100" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.price" />
                </template>
              </el-table-column>
              <el-table-column label="促销价格" width="100" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.promotionPrice" />
                </template>
              </el-table-column>
              <el-table-column label="商品库存" width="80" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.stock" />
                </template>
              </el-table-column>
              <el-table-column label="SKU编号" width="200" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.skuCode" disabled />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="handleRemoveProductSku(scope.$index, scope.row)"
                    >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item> -->
        </el-card>
        <el-card class="card-box">
          <div class="card-title">账号信息</div>
          <el-form-item label="权重排序：" prop="sort">
            <el-input v-model="value.sort" />
          </el-form-item>
          <el-form-item label="访问量：" prop="gameSysinfoReadcount">
            <el-input v-model="value.gameSysinfoReadcount" />
          </el-form-item>
          <el-form-item label="销售状态：" prop="stock">
            <el-radio-group v-model="value.stock">
              <el-radio :label="0">已售</el-radio>
              <el-radio :label="9">在售</el-radio>
              <el-radio :label="1">已预订</el-radio>
              <el-radio :label="8">仅议价</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否放心账号：" prop="gameGoodsFangxin">
            <el-radio-group v-model="value.gameGoodsFangxin">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否补款商品：" prop="gameGoodsBukuan">
            <el-radio-group v-model="value.gameGoodsBukuan">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否降价：" prop="gameGoodsJiangjia">
            <el-radio-group v-model="value.gameGoodsJiangjia">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="可否议价：" prop="gameGoodsYijia">
            <el-radio-group v-model="value.gameGoodsYijia">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否上架：" prop="publishStatus">
            <el-radio-group v-model="value.publishStatus">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否一手：" prop="gameGoodsYishou">
            <el-radio-group v-model="value.gameGoodsYishou">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="可否预约：" prop="gameGoodsYuyue">
            <el-radio-group v-model="value.gameGoodsYuyue">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="出售方式：" prop="gameGoodsSaletype">
            <el-radio-group v-model="value.gameGoodsSaletype">
              <el-radio :label="1">平台代售</el-radio>
              <el-radio :label="0">平台代售+合作号商回收</el-radio>
            </el-radio-group>
          </el-form-item>
          <div />
          <el-form-item style="width:460px" label="人物图片：" prop="pic">
            <single-upload
              :is-delet-water-list="value.qcsy2"
              v-model="value.pic"
              class="pic-box"
            />
          </el-form-item>
          <el-form-item label="是否去除水印：" prop="qcsy2">
            <el-radio-group v-model="value.qcsy2">
              <el-radio :label="1">去除</el-radio>
              <el-radio :label="0">不去除</el-radio>
            </el-radio-group>
          </el-form-item>
          <div />
          <el-form-item label="图片详情：" class="pics-box">
            <span>是否去除水印： </span>
            <el-radio-group v-model="value.qcsy3">
              <el-radio :label="1">去除</el-radio>
              <el-radio :label="0">不去除</el-radio>
            </el-radio-group>
            <multi-upload
              ref="muupload"
              :options="typeOptions"
              :is-delet-water-list="value.qcsy3"
              :hasImgType="hasImgType"
              v-model="selectProductPics"
            />
          </el-form-item>
        </el-card>

        <el-card v-if="extList['ext2'].needShow" style="margin-bottom:20px;">
          <!--扩展属性-->
          <el-col>
            <div shadow="never" class="cardBg">
              <tedian
                :detail-options="extList['ext2'].detailOptions"
                :opetion-date="extList['ext2'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
          </el-col>
        </el-card>

        <el-card class="card-box">
          <el-form-item
            label="账号描述："
            style="width:100%;"
            prop="description"
          >
            <el-input v-model="value.description" type="textarea" />
          </el-form-item>
        </el-card>

        <el-card class="card-box">
          <div class="card-title">
            保障信息
          </div>
          <el-form-item label="联系手机：" prop="gameCareinfoPhone">
            <el-input v-model="value.gameCareinfoPhone" />
          </el-form-item>
          <el-form-item label="联系时间：" prop="gameCareinfoTime">
            <el-input v-model="value.gameCareinfoTime" />
          </el-form-item>
          <el-form-item label="联系微信：" prop="gameCareinfoVx">
            <el-input v-model="value.gameCareinfoVx" />
          </el-form-item>
        </el-card>

        <el-card v-if="extList['ext3'].needShow">
          <!--扩展属性-->
          <el-col>
            <div shadow="never" class="cardBg">
              <tedian
                :detail-options="extList['ext3'].detailOptions"
                :opetion-date="extList['ext3'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
          </el-col>
        </el-card>
        <el-card v-if="extList['ext4'].needShow">
          <!--扩展属性-->
          <el-col>
            <div shadow="never" class="cardBg">
              <tedian
                :detail-options="extList['ext4'].detailOptions"
                :opetion-date="extList['ext4'].opetionDate"
                @changequfu="changequfu"
              />
            </div>
          </el-col>
        </el-card>
        <el-card style="margin-top:20px;">
          <el-form-item label="审核备注：" prop="mark">
            <el-input v-model="detail" />
          </el-form-item>
        </el-card>
      </div>
      <!-- <div v-show="tabValue === '1'">
        <el-form-item label="首页推荐：" prop="recommandStatus">
          <el-switch
            v-model="value.recommandStatus"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="关联专区：">
          <el-transfer
            :filter-method="filterMethod"
            v-model="selectSubject"
            :titles="subjectTitles"
            :data="subjectList"
            style="display: inline-block"
            filterable
            filter-placeholder="请输入专题名称"
          />
        </el-form-item>
      </div>
      <div v-show="tabValue === '2'">
        <el-card v-if="this.memberId" class="card-box">
          <el-form-item label="昵称">
            {{ memberInfo.nickname }}
          </el-form-item>
          <el-form-item label="性别">
            {{ getGender(memberInfo.gender) }}
          </el-form-item>
          <el-form-item label="手机号"> {{ memberInfo.phone }}</el-form-item>
          <el-form-item label="IM号"> {{ memberInfo.imaccount }}</el-form-item>
          <el-form-item label="是否实名认证">
            {{ getConfirm(memberInfo.realNameConfirm) }}</el-form-item
          >
          <el-form-item label="是否实人认证">
            {{ getConfirm(memberInfo.realPersionConfirm) }}</el-form-item
          >
          <el-form-item label="是否包赔认证">
            {{ getConfirm(memberInfo.baopeiConfirm) }}</el-form-item
          >
        </el-card>
        <el-card v-else>
          <div>后台录入，无用户信息</div>
        </el-card>
      </div> -->
    </el-form>
    <div class="m-footer">
      <el-button @click="submitForm('productForm', 2)">拒绝</el-button>
      <el-button type="primary" @click="submitForm('productForm', 1)"
        >通过</el-button
      >
    </div>
  </div>
</template>

<script>
import _ from 'lodash';
import { doVerifyStatus } from '@/api/product';
import { fetchListWithChildren, getProductCate } from '@/api/productCate';
import { fetchList as fetchBrandList } from '@/api/brand';
import { fetchList as fetchProductAttrCateList } from '@/api/productAttrCate';
import { fetchList as fetchProductAttrList } from '@/api/productAttr';
import { getMemberDetail } from '@/api/member';
import { fetchList as fetchMemberLevelList } from '@/api/memberLevel';

import { createProduct, getProduct, updateProduct } from '@/api/product';

import { fetchListAll as fetchSubjectList } from '@/api/subject';
// import { fetchList as fetchPrefrenceAreaList } from '@/api/prefrenceArea'
import SingleUpload from '@/components/Upload/singleUpload';
import MultiUpload from '@/components/Upload/multiUpload';
import Tinymce from '@/components/Tinymce';

import tedian from '@/components/tedian';
import util from '@/utils/index';

const defaultProductParam = {
  albumPics: '',
  albumPicsJson: '[]',
  productAttributeValueList: [],
  brandId: '',
  productAttributeCategoryId: '',
  gameAccountQufu: '',
  description: '',
  qcsy2: 0,
  pic: '',
  qcsy3: 0,
  selectProductPics: [],
  price: '',
  originalPrice: '',
  stock: 9,
  gameGoodsFangxin: 0,
  gameGoodsBukuan: 0,
  gameGoodsJiangjia: 0,
  gameGoodsYijia: 0,
  sort: '',
  publishStatus: 0,
  gameGoodsYishou: 0,
  gameGoodsYuyue: 0,
  gameGoodsSaletype: 1,
  gameCareinfoPhone: '',
  gameCareinfoTime: '',
  gameCareinfoVx: '',
  subjectProductRelationList: []
};
const OPTSOUT = {
  '1': '面板属性',
  '2': '打造内功',
  '3': '天赏外观',
  '4': '普通外观',
  '5': '其他物品'
};
// var idUUid = 0
export default {
  components: { SingleUpload, MultiUpload, Tinymce, tedian },
  props: {
    queryId: {
      type: [Number, String],
      default: ''
    },
    cateParentId: {
      type: [Number, String],
      default: ''
    },
    productCategoryId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      typeOptions: [],
      hasImgType: false,
      detail: '',
      tabValue: '0',
      // detail
      active: 0,
      value: Object.assign({}, defaultProductParam),
      // info
      hasEditCreated: false,
      // 选中商品分类的值
      selectProductCateValue: [],
      productCateOptions: [],
      brandOptions: [],
      //  sale
      //  attr
      // 商品属性分类下拉选项
      productAttributeCategoryOptions: [],
      // 选中的商品属性
      // selectProductAttr: [],
      // 选中的商品参数
      selectProductParam: [],
      // 选中的商品属性图片
      // selectProductAttrPics: [],
      // 可手动添加的商品属性
      addProductAttrValue: '',
      // 商品富文本详情激活类型
      activeHtmlName: 'pc',
      // 扩展属性
      // extList: [],

      rules: {
        // name: [
        //   { required: true, message: '请输入商品名称', trigger: 'blur' },
        //   {
        //     min: 2,
        //     max: 140,
        //     message: '长度在 2 到 140 个字符',
        //     trigger: 'blur'
        //   }
        // ],
        // subTitle: [
        //   { required: true, message: '请输入商品副标题', trigger: 'blur' }
        // ],
        // productCategoryId: [
        //   { required: true, message: '请选择商品分类', trigger: 'blur' }
        // ],
        // brandId: [
        //   { required: true, message: '请选择商品品牌', trigger: 'blur' }
        // ],
        // description: [
        //   { required: true, message: '请输入商品介绍', trigger: 'blur' }
        // ],
        // requiredProp: [
        //   { required: true, message: '该项为必填项', trigger: 'blur' }
        // ]
      },
      formLabelWidth: '200px',
      spanCol: 12,
      extList: {
        ext1: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext2: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext3: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext4: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext5: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext6: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        }
      },
      // 所有专题列表
      subjectList: [],
      // 专题左右标题
      subjectTitles: ['待选择', '已选择'],
      memberId: '',
      memberInfo: {}
    };
  },
  computed: {
    isEdit() {
      return this.queryId !== '';
    },
    // 商品的编号
    productId() {
      return this.value.id;
    },
    // sale
    // 选中的服务保证
    selectServiceList: {
      get() {
        const list = [];
        if (
          this.value.serviceIds === undefined ||
          this.value.serviceIds == null ||
          this.value.serviceIds === ''
        ) {
          return list;
        }
        const ids = this.value.serviceIds.split(',');
        for (let i = 0; i < ids.length; i++) {
          list.push(Number(ids[i]));
        }
        return list;
      },
      set(newValue) {
        let serviceIds = '';
        if (newValue != null && newValue.length > 0) {
          for (let i = 0; i < newValue.length; i++) {
            serviceIds += newValue[i] + ',';
          }
          if (serviceIds.endsWith(',')) {
            serviceIds = serviceIds.substr(0, serviceIds.length - 1);
          }
          this.value.serviceIds = serviceIds;
        } else {
          this.value.serviceIds = null;
        }
      }
    },
    // attr
    // 是否有商品属性图片
    // hasAttrPic() {
    //   if (this.selectProductAttrPics.length < 1) {
    //     return false
    //   }
    //   return true
    // },
    // 商品的主图和画册图片
    selectProductPics: {
      get: function() {
        const pics = [];
        if (
          this.value.albumPics === undefined ||
          this.value.albumPics == null ||
          this.value.albumPics === ''
        ) {
          return pics;
        }
        const albumPics = this.value.albumPics.split(',');
        let albumPicsJson = this.value.albumPicsJson || '[]';
        albumPicsJson = JSON.parse(albumPicsJson);

        for (let i = 0; i < albumPics.length; i++) {
          let name = '';
          if (albumPicsJson[i]) {
            let tempName = albumPicsJson[i].name;
            name = tempName || '';
          }
          if (this.hasImgType) {
            pics.push({
              url: albumPics[i],
              name
            });
          } else {
            pics.push(albumPics[i]);
          }
        }
        return pics;
      },
      set: function(newValue) {
        if (newValue == null || newValue.length === 0) {
          // this.value.pic = null
          this.value.albumPics = null;
        } else {
          // this.value.pic = newValue[0]
          let albumPics = '';
          let albumPicsJson = [];
          if (newValue.length > 0) {
            for (let i = 0; i < newValue.length; i++) {
              if (this.hasImgType) {
                albumPics += newValue[i].url;
                albumPicsJson.push({
                  url: newValue[i].url,
                  name: newValue[i].name
                });
              } else {
                albumPics += newValue[i];
              }
              if (i !== newValue.length - 1) {
                albumPics += ',';
              }
            }
            this.value.albumPics = albumPics;
            this.value.albumPicsJson = JSON.stringify(albumPicsJson);
          }
        }
      }
    },
    // 选中的专题
    selectSubject: {
      get: function() {
        const subjects = [];
        if (
          this.value.subjectProductRelationList == null ||
          this.value.subjectProductRelationList.length <= 0
        ) {
          return subjects;
        }
        for (let i = 0; i < this.value.subjectProductRelationList.length; i++) {
          subjects.push(this.value.subjectProductRelationList[i].subjectId);
        }
        return subjects;
      },
      set: function(newValue) {
        this.value.subjectProductRelationList = [];
        for (let i = 0; i < newValue.length; i++) {
          this.value.subjectProductRelationList.push({
            subjectId: newValue[i]
          });
        }
      }
    }
  },
  watch: {
    // info
    selectProductCateValue: function(newValue) {
      if (newValue != null && newValue.length === 2) {
        this.value.productCategoryId = newValue[1];
        this.value.productCategoryName = this.getCateNameById(
          this.value.productCategoryId
        );
      } else {
        this.value.productCategoryId = null;
        this.value.productCategoryName = null;
      }
    },
    // info
    productId: function(newValue) {
      if (!this.isEdit) return;
      if (this.hasEditCreated) return;
      if (newValue === undefined || newValue == null || newValue === 0) return;
      this.handleEditCreatedInfo();
      this.handleEditCreatedAttr();
    }
  },
  created() {
    // index
    // info
    this.getProductCate();
    this.getProductCateList();
    this.getBrandList();

    // sale
    // if (this.isEdit) {
    // this.handleEditCreated();
    // } else {
    // fetchMemberLevelList({ defaultStatus: 0 }).then(response => {
    //   const memberPriceList = []
    //   for (let i = 0; i < response.data.length; i++) {
    //     const item = response.data[i]
    //     memberPriceList.push({
    //       memberLevelId: item.id,
    //       memberLevelName: item.name
    //     })
    //   }
    //   this.value.memberPriceList = memberPriceList
    // })
    // }
    // attr
    this.getProductAttrCateList();
    // relation
    // this.getSubjectList();
    // this.getPrefrenceAreaList()
  },
  methods: {
    getProductCate() {
      getProductCate(this.productCategoryId).then(res => {
        if (res.code == 200) {
          const { data } = res;
          if (data.custom) {
            let custom = JSON.parse(data.custom);
            if (
              custom.albumPicsTypeOptions &&
              custom.albumPicsTypeOptions.length
            ) {
              this.typeOptions = custom.albumPicsTypeOptions;
              this.hasImgType = true;
            }
          }
          if (this.isEdit) {
            getProduct(this.queryId).then(response => {
              this.value = Object.assign({}, this.value, response.data);
              if (this.hasImgType) {
                this.transFormAlbumPicsJson();
              }
              this.memberId = this.value.memberId || '';
              this.saveSkuStockList = _.cloneDeep(this.value.skuStockList);
            });
          }
        }
      });
    },
    transFormAlbumPicsJson() {
      let albumPicsJson = this.value.albumPicsJson || '[]';
      albumPicsJson = JSON.parse(albumPicsJson);
      let tempList = [];
      albumPicsJson.forEach(ele => {
        // 如果有 type，没有 name，把 type 映射成 name
        if (!ele.hasOwnProperty('name')) {
          ele.name = OPTSOUT[ele.type] || ele.type || '';
          tempList.push(ele);
        } else {
          tempList.push(ele);
        }
      });
      this.value.albumPicsJson = JSON.stringify(tempList);
    },
    getGender(gender) {
      return gender == 1 ? '男' : '女';
    },
    getConfirm(value) {
      return value === 1 ? '是' : '否';
    },
    // handleRemoveProductSku(index, row) {
    //   const list = this.value.skuStockList;
    //   if (list.length === 1) {
    //     list.pop();
    //   } else {
    //     list.splice(index, 1);
    //   }
    //   const tempList = [];
    //   list.forEach(ele => {
    //     const spData = JSON.parse(ele.spData);
    //     const value = spData[0].value;
    //     tempList.push(value);
    //   });
    //   // 目前只有一个，第一个就是
    //   this.$set(this.selectProductAttr[0], 'values', tempList);
    // },
    changequfu(value) {
      this.value.gameAccountQufu = value;
    },
    changeTab(tab, event) {
      if (tab.index === '2') {
        this.getMember();
      }
    },
    getMember() {
      if (this.memberId) {
        getMemberDetail(this.memberId).then(res => {
          this.memberInfo = res.data;
        });
      }
    },
    // info
    // 处理编辑逻辑
    handleEditCreatedInfo() {
      if (this.value.productCategoryId != null) {
        this.selectProductCateValue = [];
        this.selectProductCateValue.push(this.value.cateParentId);
        this.selectProductCateValue.push(this.value.productCategoryId);
      }
      this.hasEditCreated = true;
    },
    getProductCateList() {
      fetchListWithChildren().then(response => {
        const list = response.data;
        this.productCateOptions = [];
        for (let i = 0; i < list.length; i++) {
          const children = [];
          if (list[i].children != null && list[i].children.length > 0) {
            for (let j = 0; j < list[i].children.length; j++) {
              children.push({
                label: list[i].children[j].name,
                value: list[i].children[j].id
              });
            }
          }
          this.productCateOptions.push({
            label: list[i].name,
            value: list[i].id,
            children: children
          });
        }
        // 如果是新建触发一下值改动
        if (this.cateParentId && this.productCategoryId) {
          this.selectProductCateValue = [];
          this.selectProductCateValue.push(parseInt(this.cateParentId, 10));
          this.selectProductCateValue.push(
            parseInt(this.productCategoryId, 10)
          );
        }
      });
    },
    getBrandList() {
      fetchBrandList({ pageNum: 1, pageSize: 200 }).then(response => {
        this.brandOptions = [];
        const brandList = response.data.list;
        for (let i = 0; i < brandList.length; i++) {
          this.brandOptions.push({
            label: brandList[i].name,
            value: brandList[i].id
          });
        }
      });
    },
    getCateNameById(id) {
      let name = null;
      for (let i = 0; i < this.productCateOptions.length; i++) {
        for (let j = 0; j < this.productCateOptions[i].children.length; j++) {
          if (this.productCateOptions[i].children[j].value === id) {
            name = this.productCateOptions[i].children[j].label;
            return name;
          }
        }
      }
      return name;
    },
    handleBrandChange(val) {
      let brandName = '';
      for (let i = 0; i < this.brandOptions.length; i++) {
        if (this.brandOptions[i].value === val) {
          brandName = this.brandOptions[i].label;
          break;
        }
      }
      this.value.brandName = brandName;
    },
    // sale
    handleEditCreated() {
      const ids = this.value.serviceIds.split(',');
      console.log('handleEditCreated', ids);
      for (let i = 0; i < ids.length; i++) {
        this.selectServiceList.push(Number(ids[i]));
      }
    },
    handleRemoveProductLadder(index, row) {
      const productLadderList = this.value.productLadderList;
      if (productLadderList.length === 1) {
        productLadderList.pop();
        productLadderList.push({
          count: 0,
          discount: 0,
          price: 0
        });
      } else {
        productLadderList.splice(index, 1);
      }
    },
    handleAddProductLadder(index, row) {
      const productLadderList = this.value.productLadderList;
      if (productLadderList.length < 3) {
        productLadderList.push({
          count: 0,
          discount: 0,
          price: 0
        });
      } else {
        this.$message({
          message: '最多只能添加三条',
          type: 'warning'
        });
      }
    },
    handleRemoveFullReduction(index, row) {
      const fullReductionList = this.value.productFullReductionList;
      if (fullReductionList.length === 1) {
        fullReductionList.pop();
        fullReductionList.push({
          fullPrice: 0,
          reducePrice: 0
        });
      } else {
        fullReductionList.splice(index, 1);
      }
    },
    handleAddFullReduction(index, row) {
      const fullReductionList = this.value.productFullReductionList;
      if (fullReductionList.length < 3) {
        fullReductionList.push({
          fullPrice: 0,
          reducePrice: 0
        });
      } else {
        this.$message({
          message: '最多只能添加三条',
          type: 'warning'
        });
      }
    },
    // attr
    handleEditCreatedAttr() {
      // 根据商品属性分类id获取属性和参数
      if (this.value.productAttributeCategoryId != null) {
        this.handleProductAttrChange(this.value.productAttributeCategoryId);
      }
      this.hasEditCreated = true;
    },
    getProductAttrCateList() {
      const param = { pageNum: 1, pageSize: 999 };
      fetchProductAttrCateList(param).then(response => {
        this.productAttributeCategoryOptions = [];
        const list = response.data.list;
        for (let i = 0; i < list.length; i++) {
          this.productAttributeCategoryOptions.push({
            label: list[i].name,
            value: list[i].id
          });
        }
      });
    },
    getProductAttrList(type, cid) {
      const param = { pageNum: 1, pageSize: 200, type: type };
      fetchProductAttrList(cid, param).then(response => {
        const list = response.data.list;
        if (type !== 0) {
          let label = `基础信息扩展`;
          if (type === 2) {
            label = `账号信息扩展`;
          } else if (type === 3) {
            label = `其他扩展`;
          }
          const extParam = {
            index: parseInt(type, 10),
            label,
            needShow: list && list.length > 0
          };
          const opetionDate = this.getEditAttrOptions2(list);
          const detailoptList = [];
          extParam.opetionDate = opetionDate;
          for (let i = 0; i < list.length; i++) {
            let detailopt = null;
            if (this.isEdit) {
              // 编辑模式下获取参数属性
              detailopt = this.getEditParamValue2(list[i]);
              detailopt && detailoptList.push(detailopt);
            }
          }
          extParam.detailOptions = detailoptList;
          util.async2opetionDate(extParam.detailOptions, extParam.opetionDate);
          this.$set(this.extList, `ext${type}`, extParam);
        } else {
          // if (!this.isEdit) {
          //   this.selectProductAttr = [];
          // }
          // for (let i = 0; i < list.length; i++) {
          //   const options = [];
          //   let values = [];
          //   if (this.isEdit) {
          //     // if (list[i].handAddStatus === 1) {
          //     //   // 编辑状态下获取手动添加编辑属性
          //     //   options = this.getEditAttrOptions(list[i].id)
          //     // }
          //     // 编辑状态下获取选中属性
          //     values = this.getEditAttrValues(i);
          //   }
          //   this.selectProductAttr.push({
          //     id: list[i].id,
          //     name: list[i].name,
          //     handAddStatus: list[i].handAddStatus,
          //     inputList: list[i].inputList,
          //     values: values,
          //     options: options
          //   });
          // }
          // if (this.isEdit) {
          //   // 编辑模式下刷新商品属性图片
          //   this.refreshProductAttrPics()
          // }
        }
      });
    },
    // getProductSkuSp(row, index) {
    //   const spData = JSON.parse(row.spData);
    //   if (spData != null && index < spData.length) {
    //     return spData[index].value;
    //   } else {
    //     return null;
    //   }
    // },
    // priceChange(e) {
    //   if (!this.isEdit) {
    //     this.refreshProductSkuList()
    //   }
    // },
    // refreshProductSkuList(checkboxList) {
    //   this.value.skuStockList = this.value.skuStockList || [];
    //   const skuList = this.value.skuStockList;
    //   // 只有一个属性时
    //   if (this.selectProductAttr.length === 1) {
    //     if (checkboxList.length > skuList.length) {
    //       // add
    //       const attrName = checkboxList[checkboxList.length - 1];
    //       const obj = {
    //         spData: JSON.stringify([{ key: attrName, value: attrName }])
    //       };
    //       if (this.isEdit) {
    //         const findOld = this.saveSkuStockList.find(ele => {
    //           return JSON.parse(ele.spData)[0].value === attrName;
    //         });
    //         if (findOld && findOld.skuCode) {
    //           obj.skuCode = findOld.skuCode;
    //         }
    //       }
    //       skuList.push(obj);
    //     } else {
    //       const index = skuList.findIndex(ele => {
    //         const value = JSON.parse(ele.spData)[0].value;
    //         const find = checkboxList.find(attrName => {
    //           return value === attrName;
    //         });
    //         if (!find) {
    //           return true;
    //         }
    //       });
    //       skuList.splice(index, 1);
    //       // delete
    //     }
    //   }

    //   this.$forceUpdate();
    // else if (this.selectProductAttr.length === 2) {
    //   const attr0 = this.selectProductAttr[0]
    //   const attr1 = this.selectProductAttr[1]
    //   for (let i = 0; i < attr0.values.length; i++) {
    //     if (attr1.values.length === 0) {
    //       skuList.push({
    //         spData: JSON.stringify([
    //           { key: attr0.name, value: attr0.values[i] }
    //         ])
    //       })
    //       continue
    //     }
    //     for (let j = 0; j < attr1.values.length; j++) {
    //       const spData = []
    //       spData.push({ key: attr0.name, value: attr0.values[i] })
    //       spData.push({ key: attr1.name, value: attr1.values[j] })
    //       skuList.push({
    //         spData: JSON.stringify(spData)
    //       })
    //     }
    //   }
    // } else {
    //   const attr0 = this.selectProductAttr[0]
    //   const attr1 = this.selectProductAttr[1]
    //   const attr2 = this.selectProductAttr[2]
    //   for (let i = 0; i < attr0.values.length; i++) {
    //     if (attr1.values.length === 0) {
    //       skuList.push({
    //         spData: JSON.stringify([
    //           { key: attr0.name, value: attr0.values[i] }
    //         ])
    //       })
    //       continue
    //     }
    //     for (let j = 0; j < attr1.values.length; j++) {
    //       if (attr2.values.length === 0) {
    //         const spData = []
    //         spData.push({ key: attr0.name, value: attr0.values[i] })
    //         spData.push({ key: attr1.name, value: attr1.values[j] })
    //         skuList.push({
    //           spData: JSON.stringify(spData)
    //         })
    //         continue
    //       }
    //       for (let k = 0; k < attr2.values.length; k++) {
    //         const spData = []
    //         spData.push({ key: attr0.name, value: attr0.values[i] })
    //         spData.push({ key: attr1.name, value: attr1.values[j] })
    //         spData.push({ key: attr2.name, value: attr2.values[k] })
    //         skuList.push({
    //           spData: JSON.stringify(spData)
    //         })
    //       }
    //     }
    //   }
    // }
    // },
    // refreshProductAttrPics() {
    //   this.selectProductAttrPics = []
    //   if (this.selectProductAttr.length >= 1) {
    //     const values = this.selectProductAttr[0].values
    //     for (let i = 0; i < values.length; i++) {
    //       let pic = null
    //       if (this.isEdit) {
    //         // 编辑状态下获取图片
    //         pic = this.getProductSkuPic(values[i])
    //       }
    //       this.selectProductAttrPics.push({ name: values[i], pic: pic })
    //     }
    //   }
    // },
    // 获取商品相关属性的图片
    // getProductSkuPic(name) {
    //   for (let i = 0; i < this.value.skuStockList.length; i++) {
    //     const spData = JSON.parse(this.value.skuStockList[i].spData)
    //     if (name === spData[0].value) {
    //       return this.value.skuStockList[i].pic
    //     }
    //   }
    //   return null
    // },
    // getInputListArr(inputList) {
    //   return inputList.split(',');
    // },
    // selectProductAttrChange(e) {
    //   // this.refreshProductAttrPics()
    //   this.refreshProductSkuList(e);
    // },
    // getOptionStr(arr) {
    //   let str = '';
    //   for (let i = 0; i < arr.length; i++) {
    //     str += arr[i];
    //     if (i != arr.length - 1) {
    //       str += ',';
    //     }
    //   }
    //   return str;
    // },
    // 合并商品属性
    // mergeProductAttrValue() {
    //   for (let i = 0; i < this.selectProductAttr.length; i++) {
    //     const attr = this.selectProductAttr[i]
    //     if (
    //       attr.handAddStatus === 1 &&
    //       attr.options != null &&
    //       attr.options.length > 0
    //     ) {
    //       this.value.productAttributeValueList.push({
    //         productAttributeId: attr.id,
    //         value: this.getOptionStr(attr.options)
    //       })
    //     }
    //   }
    //   for (let i = 0; i < this.selectProductParam.length; i++) {
    //     const param = this.selectProductParam[i]
    //     this.value.productAttributeValueList.push({
    //       productAttributeId: param.id,
    //       value: param.value
    //     })
    //   }
    // },
    // 合并商品属性图片
    // mergeProductAttrPics() {
    //   for (let i = 0; i < this.selectProductAttrPics.length; i++) {
    //     for (let j = 0; j < this.value.skuStockList.length; j++) {
    //       const spData = JSON.parse(this.value.skuStockList[j].spData)
    //       if (spData[0].value === this.selectProductAttrPics[i].name) {
    //         this.value.skuStockList[j].pic = this.selectProductAttrPics[i].pic
    //       }
    //     }
    //   }
    // },
    getEditAttrOptions2(list) {
      const result = list.map(item => {
        // 1输入框，2单选，3多选
        let tdtype = 1;
        if (item.inputType === 1) {
          if (item.selectType === 1) {
            tdtype = 2;
          } else if (item.selectType === 2) {
            tdtype = 3;
          } else if (item.selectType === 3) {
            tdtype = 4;
          }
        }
        if (tdtype === 1) {
          return Object.assign({}, item, {
            childList: [],
            is_required: 0,
            field_type: 2,
            default_word: '请输入',
            tdtype,
            iptVal: ''
          });
        } else if (tdtype === 2) {
          return Object.assign({}, item, {
            tdtype,
            value: '',
            is_required: 0,
            field_type: 2,
            inputList: item.inputList.split(',')
          });
        } else if (tdtype === 3) {
          const inputList = item.inputList.split(',');
          const childList = [];
          inputList.forEach(ele => {
            childList.push({
              icon: '',
              name: ele,
              checked: false
            });
          });
          return Object.assign({}, item, {
            childList,
            is_required: 0,
            field_type: 2,
            default_word: '点击可下拉选择物品',
            tdtype,
            iptVal: '',
            showChild: false,
            choosedList: [],
            zidingyiList: [],
            iptSearchVal: '',
            searchList: []
          });
        } else {
          const options = JSON.parse(item.inputList);
          options.forEach(ele => {
            ele.value = ele.parent_name;
            ele.label = ele.parent_name;
            const { childList } = ele;
            const children = childList.map(item => {
              return {
                value: item,
                label: item
              };
            });
            ele.children = children;
          });
          return Object.assign({}, item, {
            tdtype,
            value: [],
            is_required: 0,
            field_type: 2,
            options
          });
        }
      });
      return result;
    },
    // 获取选中的属性值
    // getEditAttrValues(index) {
    //   const values = new Set();
    //   if (index === 0) {
    //     for (let i = 0; i < this.value.skuStockList.length; i++) {
    //       const sku = this.value.skuStockList[i];
    //       const spData = JSON.parse(sku.spData);
    //       if (spData != null && spData.length >= 1) {
    //         values.add(spData[0].value);
    //       }
    //     }
    //   } else if (index === 1) {
    //     for (let i = 0; i < this.value.skuStockList.length; i++) {
    //       const sku = this.value.skuStockList[i];
    //       const spData = JSON.parse(sku.spData);
    //       if (spData != null && spData.length >= 2) {
    //         values.add(spData[1].value);
    //       }
    //     }
    //   } else {
    //     for (let i = 0; i < this.value.skuStockList.length; i++) {
    //       const sku = this.value.skuStockList[i];
    //       const spData = JSON.parse(sku.spData);
    //       if (spData != null && spData.length >= 3) {
    //         values.add(spData[2].value);
    //       }
    //     }
    //   }
    //   return Array.from(values);
    // },
    getEditParamValue2(item) {
      // 1输入框，2单选，3多选,4级联
      let tdtype = 1;
      if (item.inputType === 1) {
        if (item.selectType === 1) {
          tdtype = 2;
        } else if (item.selectType === 2) {
          tdtype = 3;
        } else if (item.selectType === 3) {
          tdtype = 4;
        }
      }
      for (let i = 0; i < this.value.productAttributeValueList.length; i++) {
        if (
          item.id === this.value.productAttributeValueList[i].productAttributeId
        ) {
          const v = this.value.productAttributeValueList[i];
          if (tdtype === 1) {
            return Object.assign({}, item, {
              childList: [],
              is_required: 0,
              field_type: 2,
              default_word: '请输入',
              tdtype,
              iptVal: v.value
            });
          } else if (tdtype === 2) {
            return Object.assign({}, item, {
              childList: [],
              is_required: 0,
              field_type: 2,
              default_word: '请输入',
              tdtype,
              value: v.value
            });
          } else if (tdtype === 3) {
            let values = [];
            if (v.value !== '') {
              values = v.value.split(',');
            }
            const result = [];
            values.forEach(ele => {
              result.push({
                icon: '',
                name: ele,
                checked: true
              });
            });
            return Object.assign({}, item, {
              title: item.name,
              tdtype,
              value: result
            });
          } else {
            return Object.assign({}, item, {
              title: item.name,
              tdtype,
              value: (v.value || '').split('|'),
              options: JSON.parse(item.inputList)
            });
          }
        }
      }
    },
    handleProductAttrChange(value) {
      this.extList = {
        ext1: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext2: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext3: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext4: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext5: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        },
        ext6: {
          needShow: false,
          opetionDate: [],
          detailOptions: []
        }
      };
      // this.getProductAttrList(0, value);
      this.getProductAttrList(1, value);
      this.getProductAttrList(2, value);
      this.getProductAttrList(3, value);
      this.getProductAttrList(4, value);
      this.getProductAttrList(5, value);
      this.getProductAttrList(6, value);
    },
    getParamInputList(inputList) {
      return inputList.split(',');
    },
    submitForm(formName, verifyStatus) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          // this.mergeProductAttrPics()
          this.finishCommit(verifyStatus);
        } else {
          this.$message({
            message: '验证失败',
            type: 'error',
            duration: 1000
          });
          return false;
        }
      });
    },
    filterMethod(query, item) {
      return item.label.indexOf(query) > -1;
    },
    // getSubjectList() {
    //   fetchSubjectList().then(response => {
    //     const list = response.data;
    //     for (let i = 0; i < list.length; i++) {
    //       if (list[i].categoryId !== 4 && list[i].categoryId !== 5) {
    //         this.subjectList.push({
    //           label: list[i].title,
    //           key: list[i].id
    //         });
    //       }
    //     }
    //   });
    // },
    cancel() {
      this.$emit('addsuc');
    },
    finishCommit(verifyStatus) {
      this.$confirm('是否要提交', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 组装扩展数据的值
        this.value.productAttributeValueList = [];
        const keys = Object.keys(this.extList);
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];
          const opetionDate = this.extList[key].opetionDate;
          for (let i = 0; i < opetionDate.length; i++) {
            const param = opetionDate[i];
            let value = param.value || '';
            // type1输入框，2单选，3多选，4区服
            if (param.tdtype === 3 && param.choosedList.length) {
              value = param.choosedList.map(c => c.name).join(',');
            } else if (param.tdtype === 1) {
              value = param.iptVal;
            } else if (param.tdtype === 4) {
              value = param.value.join('|');
            }
            this.value.productAttributeValueList.push({
              productAttributeId: param.id,
              value,
              attriName: param.name,
              sort: param.sort,
              filterType: param.filterType,
              searchType: param.searchType,
              type: param.type,
              searchSort: param.searchSort
            });
          }
        }

        if (!this.hasImgType) {
          this.value.albumPicsJson = '[]';
        }
        // 商品规格属性
        // this.mergeProductAttrValue()
        doVerifyStatus(this.value.id, {
          pmsProductParam: this.value,
          detail: this.detail,
          verifyStatus
        }).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '提交成功',
              duration: 1000
            });
            this.$emit('addsuc');
          }
        });
      });
    }
  }
};
</script>

<style scoped>
.card-title {
  margin-bottom: 20px;
}
.m-footer {
  text-align: right;
  margin-top: 10px;
}
.paramInputLabel {
  display: inline-block;
  width: 100px;
  text-align: left;
}
.paramInput {
  width: 200px;
}
.littleMarginTop {
  margin-top: 20px;
}
.card-box {
  margin-bottom: 20px;
}
.card-box /deep/ .el-card__body .el-form-item {
  width: 45%;
  display: inline-block;
  vertical-align: text-top;
}
.pic-box {
  width: 310px;
  display: inline-block;
  margin-left: 10px;
}
.pic-box /deep/ .el-upload-list--picture {
  width: 146px;
  height: 146px;
}
.pic-box /deep/ .el-upload-list--picture .el-upload-list__item {
  width: 146px;
  height: 146px;
  padding: 0;
  overflow: hidden;
}
.pic-box /deep/ .el-upload-list--picture .el-upload-list__item img {
  width: 146px;
  height: 146px;
  margin: 0;
}
.pic-box /deep/ .el-upload-list > :nth-child(2) {
  display: none;
}
.form-box /deep/ .el-input {
  width: 100%;
}
.form-box /deep/ .el-select {
  width: 100%;
}
.form-box /deep/ .el-cascader {
  width: 100%;
}
.pics-box {
  width: 100% !important;
}
.el-upload-list__item.is-success .el-upload-list__item-status-label {
  z-index: 99;
}
</style>
