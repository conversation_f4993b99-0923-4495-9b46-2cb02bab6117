<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="never">
      <div style="margin-top: 15px">
        <el-form
          :inline="true"
          :model="listQuery"
          size="small"
          label-width="140px"
        >
          <el-form-item label="游戏分类：">
            <el-select
              style="width:248px"
              v-model="listQuery.productCategoryId"
            >
              <el-option
                v-for="item in listCategory"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="议价状态：">
            <el-select
              v-model="listQuery.negotiationStatus"
              class="input-width"
              clearable
            >
              <el-option
                v-for="item in negotiationStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发起议价用户：">
            <el-input
              v-model="listQuery.buyerUsername"
              class="input-width"
              placeholder="发起议价用户"
            ></el-input>
          </el-form-item>
          <el-form-item label="接受议价用户：">
            <el-input
              v-model="listQuery.sellerUsername"
              class="input-width"
              placeholder="接受议价用户"
            ></el-input>
          </el-form-item>
          <el-form-item label="商品编号：">
            <el-input
              v-model="listQuery.productSn"
              class="input-width"
              placeholder="商品编号"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="付款方式：">
            <el-select
              v-model="listQuery.payType"
              class="input-width"
              placeholder="全部"
              clearable
            >
              <el-option
                v-for="item in payTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="平台：">
            <el-select
              v-model="listQuery.sourceType"
              class="input-width"
              placeholder="全部"
              clearable
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="订单状态：">
            <el-select
              v-model="listQuery.orderStatus"
              class="input-width"
              placeholder="全部"
              clearable
            >
              <el-option
                v-for="item in orderStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="议价创建时间：">
            <el-date-picker
              v-model="listQuery.time"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              style="float:right"
              type="primary"
              @click="handleSearchList()"
              size="small"
            >
              查询搜索
            </el-button>
            <el-button
              style="float:right;margin-right: 15px"
              @click="handleResetSearch()"
              size="small"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <div class="table-container">
      <el-table
        ref="orderTable"
        :data="list"
        style="width: 100%;"
        v-loading="listLoading"
        border
      >
        <!-- <el-table-column
          type="selection"
          width="60"
          align="center"
        ></el-table-column> -->
        <el-table-column fixed label="商品编号" width="140" align="center">
          <template slot-scope="scope">{{ scope.row.productSn }}</template>
        </el-table-column>
        <el-table-column label="游戏名称" width="200" align="center">
          <template slot-scope="scope">{{
            scope.row.productCategoryName
          }}</template>
        </el-table-column>
        <el-table-column label="发起议价用户" width="160" align="center">
          <template slot-scope="scope">{{ scope.row.buyerUsername }}</template>
        </el-table-column>
        <el-table-column label="接受议价用户" width="140" align="center">
          <template slot-scope="scope">{{ scope.row.sellerUsername }}</template>
        </el-table-column>
        <el-table-column label="原金额（元）" width="120" align="center">
          <template slot-scope="scope">￥{{ scope.row.originPrice }}</template>
        </el-table-column>
        <el-table-column label="议价金额（元）" width="120" align="center">
          <template slot-scope="scope">￥{{ scope.row.offerPrice }}</template>
        </el-table-column>
        <!-- <el-table-column label="支付金额（元）" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.payAmount }}</template>
        </el-table-column> -->
        <el-table-column label="议价状态" width="120" align="center">
          <template slot-scope="scope">{{
            getStatusName(scope.row.negotiaStatus)
          }}</template>
        </el-table-column>
        <el-table-column label="订单状态" width="120" align="center">
          <template slot-scope="scope">{{
            getOrderName(scope.row.orderStatus)
          }}</template>
        </el-table-column>
        <el-table-column label="是否还价" width="120" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.sellerOfferPrice" type="success">是</el-tag>
            <el-tag v-else type="warning">否</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="付款方式" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.payType }}</template>
        </el-table-column> -->
        <!-- <el-table-column label="支付平台订单编号" width="160" align="center">
          <template slot-scope="scope">{{ scope.row.accpTxno }}</template> -->
        </el-table-column>
        <el-table-column label="议价时间" width="160" align="center">
          <template slot-scope="scope">{{
            util.timeFormat(scope.row.createTime)
          }}</template>
        </el-table-column>
        <!-- <el-table-column label="支付时间" width="160" align="center">
          <template slot-scope="scope">{{
            util.timeFormat(scope.row.paymentTime)
          }}</template>
        </el-table-column>
        <el-table-column label="退款金额（元）" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.returnAmount }}</template>
        </el-table-column>
        <el-table-column label="退款原因" width="200" align="center">
          <template slot-scope="scope">{{ scope.row.returnType }}</template>
        </el-table-column> -->
        <el-table-column label="议价日志" width="200" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="top-start"
              title="议价日志"
              width="400"
              trigger="hover"
            >
              <div class="logs" v-for="item in scope.row.logs">
                <div>{{ item }}</div>
              </div>
              <div slot="reference">议价日志</div>
            </el-popover>
            <el-pro></el-pro>
          </template>
        </el-table-column>
        <!-- <el-table-column label="退款操作人" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.returnOperator }}</template>
        </el-table-column>
        <el-table-column label="退款时间" width="160" align="center">
          <template slot-scope="scope">{{
            util.timeFormat(scope.row.returnTime)
          }}</template>
        </el-table-column> -->
        <!-- <el-table-column label="撤销用户" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.payType }}</template>
        </el-table-column> -->
        <!-- <el-table-column label="拒绝用户" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.payType }}</template>
        </el-table-column> -->
        <!-- <el-table-column label="支付平台" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.status }}</template>
        </el-table-column> -->
        <el-table-column width="300" label="操作" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="handleViewOrder(scope.$index, scope.row)"
              >查看订单</el-button
            >
            <!-- <el-button
              v-if="canShow(scope.row)"
              size="mini"
              @click="onReturn(scope.$index, scope.row)"
              >退款</el-button
            > -->
            <el-button
              v-if="canShow(scope.row)"
              size="mini"
              @click="onOver(scope.$index, scope.row)"
              >结单</el-button
            >
            <el-button
              v-if="canFreeShow(scope.row)"
              size="mini"
              @click="onCancel(scope.$index, scope.row)"
              >取消议价</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes,prev, pager, next,jumper"
        :current-page.sync="listQuery.pageNum"
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog width="70%" :visible.sync="showOrderDetail">
      <orderDetail
        v-if="showOrderDetail"
        hideBtn="1"
        :id="orderId"
      ></orderDetail>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showOrderDetail = false">取 消</el-button>
        <el-button type="primary" @click="showOrderDetail = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import orderDetail from '@/views/oms/order/orderDetail';
import { returnApplyCreate } from '@/api/kf.js';
import { orderList, settlement ,negotiaCancel} from '@/api/order';
import { fetchList as fetchListProductCate } from '@/api/productCate';
const defaultListQuery = {
  pageNum: 1,
  pageSize: 20,
  negotiationStatus: null,
  buyerUsername: null,
  sellerUsername: null,
  productSn: null,
  payType: null,
  sourceType: null,
  orderStatus: null,
  time: null
};
const payTypeList = [
  { label: '未支付', value: '0' },
  { label: '支付宝', value: '1' },
  { label: '微信', value: '2' }
];

const orderStatusList = [
  { label: '待付款', value: '0' },
  { label: '已预定', value: '1' },
  { label: '换绑中', value: '2' },
  { label: '换绑完成', value: '3' },
  { label: '已关闭', value: '4' },
  { label: '无效订单', value: '5' },
  { label: '已退款', value: '6' },
  { label: '待汇款', value: '7' },
  { label: '汇款中', value: '8' },
  { label: '代发待审核', value: '9' },
  { label: '代发失败', value: '10' },
  { label: '汇款成功', value: '11' },
  { label: '结单', value: '12' }
];

const negotiationStatusList = [
  // { label: '议价订单创建，未支付', value: '-1' },
  { label: '买家已报价，待卖家回复', value: '0' },
  { label: '卖家已接受，待付款', value: '1' },
  { label: '已拒绝，待重新报价或取消', value: '2' },
  { label: '已取消', value: '3' },
  // { label: '全款订单已付', value: '4' },
  // { label: '卖家拒绝并拉黑', value: '5' },
  // { label: '已退款', value: '6' },
  // { label: '退款中', value: '7' },
  // { label: '议价买家违约', value: '8' }
];
import util from '@/utils/index';
export default {
  components: { orderDetail },
  data() {
    return {
      backInfo: {
        note: null,
        id: null,
        type: 0,
        returnAmount: null
      },
      util,
      listQuery: Object.assign({}, defaultListQuery),
      listLoading: true,
      list: null,
      total: null,
      listCategory: [],
      negotiationStatusList,
      orderStatusList,
      payTypeList,
      backDialogVisible: false,
      showOrderDetail: false
    };
  },
  created() {
    this.getCate();
    this.getList();
  },
  methods: {
    handleViewOrder(index, row) {
      this.showOrderDetail = true;
      this.orderId = row.orderId;
    },
    getCate() {
      fetchListProductCate(74, {
        pageNum: 1,
        pageSize: 999
      }).then(res => {
        if (res.code == 200) {
          this.listCategory = res.data.list;
        }
      });
    },
    getOrderName(status) {
      let findIt = orderStatusList.find(ele => ele.value == status);
      return findIt.label;
    },
    canShow(item) {
      let roles = this.$store.getters.roles || [];
      let hasPower =
        roles.includes('超级管理员') || roles.includes('财务管理员');
      return (
        hasPower &&
        item.orderStatus != 0 &&
        item.orderStatus != 4 &&
        item.orderStatus != 5 &&
        item.orderStatus != 6 &&
        item.negotiaStatus != 7 &&
        item.orderStatus != 12&&
        item.payChannel!='free'
      );
    },
    canFreeShow(item){
      let roles = this.$store.getters.roles || [];
      let hasPower =
        roles.includes('超级管理员') || roles.includes('财务管理员');
        return (hasPower&&
          item.payChannel=='free'&&
          item.orderStatus != 4
        )
    },
    getStatusName(status) { 
      let NAMEMAP = {
        '-1': '议价订单创建,未支付',
        '0': '买家已报价，待卖家回复',
        '1': '卖家已接受，待付款',
        '2': '已拒绝，待重新报价或取消',
        '3': '已取消',
        '4': '全款订单已付',
        '5': '卖家拒绝并拉黑',
        '6': '已退款',
        '7': '退款中',
        '6': '议价买家违约'
      };
      return NAMEMAP[status];
    },
    handleResetSearch() {
      this.listQuery = Object.assign({}, defaultListQuery);
    },
    handleSearchList() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = { ...this.listQuery };
      if (data.time) {
        data.startTime = data.time[0];
        data.endTime = data.time[1];
      }
      delete data.time;
      orderList(data).then(response => {
        this.listLoading = false;
        this.list = response.data.list;
        this.list.forEach(element => {
          if (element.logs) {
            element.logs = JSON.parse(element.logs);
          }
        });
        this.total = response.data.total;
      });
    },
    onReturn(index, item) {
      // this.backDialogVisible = true;
      const that = this;
      this.$confirm(`确认全额退款${item.payAmount}元吗？`, '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
        distinguishCancelAndClose: true //区分取消与关闭
      }).then(() => {
        const params = {
          returnAmount: item.payAmount,
          orderId: item.orderId,
          reason: '议价退款',
          type: 0
        };
        returnApplyCreate(params).then(response => {
          if(response.code==200){
          this.backDialogVisible = false;
          this.$message({
            type: 'success',
            message: '申请退款成功!'
          });
          this.getList();
        }
        });
      });
    },
    onOver(index, item) {
      const that = this;
      this.$confirm('确认结单吗？', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
        distinguishCancelAndClose: true //区分取消与关闭
      }).then(() => {
        settlement({
          id: item.orderId
        }).then(res => {
          if (res.code == 200) {
            that.$message.success(res.message);
          }
        });
      });
    },
    onCancel(index, item){
      const that = this;
      this.$confirm('确认取消议价吗？', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning',
        distinguishCancelAndClose: true //区分取消与关闭
      }).then(() => {
        negotiaCancel({
          negoId: item.negoId
        }).then(res => {
          if (res.code == 200) {
            that.$message.success('取消成功');
          }
        });
      });
    },
  }
};
</script>
<style scoped>
.input-width {
  width: 203px;
}
.logs {
  width: 100%;
  word-break: break-all;
  word-spacing: normal;
}
</style>
