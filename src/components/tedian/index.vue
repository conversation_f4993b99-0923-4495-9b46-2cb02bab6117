<template>
  <div label="">
    <div
      v-for="(item, index) in opetionDate"
      :key="index"
      class="tedian_box_ext"
    >
      <!-- 输入框情况 -->
      <div v-if="item.tdtype == 1" class="tedian_item">
        <div class="teding_name">{{ item.name }}:</div>
        <div class="right_tedian_box">
          <el-input
            v-model="item.iptVal"
            type="textarea"
            autosize
            placeholder="请输入"
            @blur="changeOpetions"
          />
        </div>
      </div>
      <!--单选-->
      <div v-if="item.tdtype == 2" class="tedian_item">
        <div class="teding_name">{{ item.name }}:</div>
        <div class="right_tedian_box">
          <el-select v-model="item.value" @change="handleChange(item)">
            <el-option
              v-for="item in item.inputList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>
      </div>
      <!-- 下拉选择+筛选 -->
      <div v-if="item.tdtype == 3" class="tedian_item">
        <div class="teding_name">{{ item.name }}:</div>
        <div class="right_tedian_box">
          <!-- 非折叠面板 -->
          <div class="searchIpt_done" @click="showChilde(item)">
            <div
              v-if="item.choosedList.length > 0 || item.zidingyiList.length > 0"
              class="spaceStart"
              style="flex-wrap: wrap;"
            >
              <div
                v-for="(v, i) in item.choosedList"
                :key="v.name"
                class="tedian_Choose_small spaceCenter active"
                @click.stop="chooseChoosed(v, item)"
              >
                <span>{{ fakeName(v.name) }}</span>
                <img
                  v-if="getIcon(v.name) === 1"
                  src="../../assets/images/jue.png"
                  class="icon"
                />
                <img
                  v-if="getIcon(v.name) === 2"
                  src="../../assets/images/price.png"
                  class="icon"
                />
                <img
                  v-if="getIcon(v.name) === 3"
                  src="../../assets/images/he.png"
                  class="icon"
                />
              </div>
            </div>
            <span v-else style="color:#606266;">&nbsp;请选择</span>
          </div>
          <div v-if="item.showChild">
            <el-input
              v-model="item.iptSearchVal"
              :id="'listNode' + item.id"
              :ref="'listNode' + item.id"
              type="text"
              placeholder="请输入搜索内容或者新游戏物品"
              style="margin-top: 10px;"
              @input="tedianChange(item)"
              @keyup.enter.native="sureAddZidingyi(item)"
            />
            <div class="tedian_container spaceStart" style="border-top: none;">
              <!-- 搜索的显示列表 -->
              <div
                v-if="item.searchList && item.searchList.length > 0"
                class="searchList_wrap"
              >
                <div class="spaceStart" style="flex-wrap: wrap;">
                  <div
                    v-for="(v, i) in item.searchList"
                    :class="v.checked ? 'active' : ''"
                    :key="v.name"
                    class="tedian_itemChoose spaceCenter"
                    @click="chooseTeDianItem(v, item)"
                  >
                    <span>{{ fakeName(v.name) }}</span>
                    <img
                      v-if="getIcon(v.name) === 1"
                      src="../../assets/images/jue.png"
                      class="icon"
                    />
                    <img
                      v-if="getIcon(v.name) === 2"
                      src="../../assets/images/price.png"
                      class="icon"
                    />
                    <img
                      v-if="getIcon(v.name) === 3"
                      src="../../assets/images/he.png"
                      class="icon"
                    />
                  </div>
                </div>
              </div>
              <div
                v-for="(v, i) in item.childList"
                :class="v.splitTitle ? 'splitTitle' : v.checked ? 'active' : ''"
                :key="v.name"
                class="tedian_itemChoose spaceCenter"
                @click="chooseTeDianItem(v, item)"
              >
                <span>{{ fakeName(v.name) }}</span>
                <span
                  v-if="needAll && v.splitTitle"
                  class="selectAll"
                  @click="toggleAll($event, item)"
                  >{{ selectAllTxt }}</span
                >
                <img
                  v-if="getIcon(v.name) === 1"
                  src="../../assets/images/jue.png"
                  class="icon"
                />
                <img
                  v-if="getIcon(v.name) === 2"
                  src="../../assets/images/price.png"
                  class="icon"
                />
                <img
                  v-if="getIcon(v.name) === 3"
                  src="../../assets/images/he.png"
                  class="icon"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 下拉选择+筛选 end -->
      <!--级联-->
      <div v-if="item.tdtype == 4" class="tedian_item">
        <div class="teding_name">{{ item.name }}:</div>
        <div class="right_tedian_box">
          <el-cascader
            v-model="item.value"
            :options="item.options"
            @change="handleChange(item)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { optionsListApi } from "@/api/index";

export default {
  props: {
    needAll: {
      type: Boolean,
      default: false
    },
    // flagId: "",
    // 编辑带过来的详情数据
    detailOptions: {
      type: Array,
      default: []
    },
    opetionDate: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      selectAllTxt: '全选',
      selectAll: false
      // detail_options: [],
      // opetionDate: []
    };
  },
  computed: {},
  created() {
    // if (this.flagId) {
    //   this.initOpetionList();
    // }
    if (this.detailOptions.length > 0 && this.opetionDate.length > 0) {
      this.initOpetionList();
    }
  },
  beforeDestroy() {
    this.opetionDate.forEach(ele => {
      ele.choosedList = [];
      ele.showChild = false;
    });
  },
  methods: {
    toggleAll(event, item) {
      this.selectAll = !this.selectAll;
      item.choosedList = [];
      if (this.selectAll) {
        this.selectAllTxt = '清空';
        item.childList.forEach(ele => {
          if (!ele.splitTitle) {
            ele.checked = true;
            item.choosedList.push(ele);
          }
        });
      } else {
        this.selectAllTxt = '全选';
        item.childList.forEach(ele => {
          if (!ele.splitTitle) {
            ele.checked = false;
          }
        });
      }
      event.stopPropagation();
    },
    getIcon(name) {
      if (name.indexOf('[绝]') !== -1) {
        return 1;
      } else if (name.indexOf('[钱]') !== -1) {
        return 2;
      } else if (name.indexOf('[核]') !== -1) {
        return 3;
      }
    },
    fakeName(name) {
      return name.replace(/\[[^\]]*\]/, '');
    },
    handleChange(item) {
      console.log(item)
      if (item.ename === 'gameAccountQufu') {
        let qufu = item.value;
        if (Array.isArray(item.value)) {
          qufu = qufu.join('|');
        }
        this.$emit('changequfu', qufu);
      }
    },
    // 处理自定义数据加入
    sureAddZidingyi(date) {
      return;
      if (!date.iptSearchVal) {
        this.$message.error('请输入搜索内容或者新游戏物品');
        return;
      }
      var isTianjia = true;
      date.choosedList.forEach((v, i) => {
        if (v.name == date.iptSearchVal) {
          this.$message.error('您已添加此数据，无需重复添加');
          isTianjia = false;
        }
      });
      if (isTianjia) {
        var json = {
          name: date.iptSearchVal
        };
        date.choosedList.push(json);
        date.iptSearchVal = '';
        date.searchList = [];
      }
    },
    // 处理已经添加的数据
    chooseChoosed(date, arrlist) {
      arrlist.choosedList.forEach((v, i) => {
        if (v.name == date.name) {
          arrlist.choosedList.splice(i, 1);
        }
      });
      arrlist.childList.forEach((v, i) => {
        if (v.name == date.name) {
          v.checked = false;
        }
      });
    },
    iconFilter(v) {
      if (v.icon == '1') {
        return '../../static/push/jue.png';
      } else if (v.icon == '2') {
        return '../../static/push/price.png';
      } else if (v.icon == '3') {
        return '../../static/push/he.png';
      }
    },
    // initOpetionList() {
    //   optionsListApi({
    //     flag_id: this.flagId
    //   }).then(response => {
    //     response.data.forEach((v, i) => {
    //       v.id = i;
    //     });
    //     this.opetionDate = response.data;
    //     // 详情赋值数据
    //     if (this.detailOptions) {
    //       this.detailOptions.forEach((item, index) => {
    //         this.opetionDate.forEach((v, i) => {
    //           if (item.name == v.name && item.type == 1) {
    //             v.iptVal = item.iptVal;
    //           }
    //           if (v.type == 2 && item.title == v.name) {
    //             v.choosedList = item.value;
    //           }
    //         });
    //       });
    //       // 给下拉展示的数据加上选中
    //       this.opetionDate.forEach((v, i) => {
    //         if (v.type == 2 && v.choosedList.length > 0) {
    //           v.choosedList.forEach((c, indexC) => {
    //             v.childList.forEach((child, indexChild) => {
    //               if (c.name == child.name) {
    //                 child.checked = true;
    //               }
    //             });
    //           });
    //         }
    //       });
    //     }
    //   });
    // },
    initOpetionList() {
      if (this.detailOptions) {
        this.detailOptions.forEach((item, index) => {
          this.opetionDate.forEach((v, i) => {
            if (item.tdtype == 1 && item.name == v.name) {
              v.iptVal = item.iptVal;
            }
            if (v.tdtype == 3 && item.title == v.name) {
              v.choosedList = item.value;
            }
            if (v.tdtype == 2 && item.name == v.name) {
              v.value = item.value;
            }
            if (v.tdtype == 4 && item.title == v.name) {
              v.value = item.value;
            }
          });
        });
        // 给下拉展示的数据加上选中
        this.opetionDate.forEach((v, i) => {
          if (v.tdtype == 3 && v.choosedList.length > 0) {
            v.choosedList.forEach((c, indexC) => {
              v.childList.forEach((child, indexChild) => {
                if (c.name == child.name) {
                  child.checked = true;
                }
              });
            });
          }
        });
      }
    },
    // 传递数据改变
    changeOpetions() {
      this.$emit('getopetion', this.opetionDate);
    },
    // 特点描述-下拉选择-重组选中数据
    chooseTeDianItem(date, arrlist) {
      if (date.splitTitle) {
        return;
      }
      if (!date.checked) {
        date.checked = true;
        arrlist.choosedList.push(date);
      } else {
        date.checked = false;
        arrlist.choosedList.forEach((v, i) => {
          if (v.name == date.name) {
            arrlist.choosedList.splice(i, 1);
          }
        });
      }
      this.changeOpetions();
    },
    // 打开-关闭下拉框
    showChilde(date) {
      date.showChild = !date.showChild;
      if (date.showChild == false) {
        date.searchList = [];
        date.iptSearchVal = '';
      }
    },
    // 关闭筛选框
    closeChooseTedian(date) {
      date.iptSearchVal = '';
      date.showChild = false;
    },
    // 下拉框里面搜索-有数据在搜索没有就不搜索
    tedianChange(e) {
      if (e.iptSearchVal) {
        const reg = new RegExp(e.iptSearchVal);
        e.searchList = e.childList.filter(
          item => reg.test(item.name) && item.splitTitle != 1
        );
      } else {
        e.searchList = [];
      }
      this.changeOpetions();
    },
    // 添加自定义数据
    addNewsChoose(date) {
      var json = {
        name: date.iptSearchVal
      };
      date.zidingyiList.push(json);
    }
  }
  // watch: {
  //   flagId(newVal, oldVal) {
  //     this.initOpetionList();
  //   }
  // }
};
</script>

<style lang="scss" scoped>
.selectAll {
  width: 50px;
  height: 30px;
  padding: 10px;
  border: 1px solid #ccc;
  cursor: pointer;
}
/****************************************/
.spaceCenter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.icon {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}
.teding_name {
  word-break: keep-all;
  font-size: 14px;
  color: #606266;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  border: 1px solid #dcdfe6;
  width: 200px;
}
.right_tedian {
  width: 100%;
  margin-left: 28px;
}
.spaceStart {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}
.ext1 {
  .tedian_box_ext {
    width: 45%;
    display: inline-block;
    .tedian_item {
      display: inline-block;
      width: 100%;
      .teding_name {
        float: left;
        border: 0;
        text-align: right;
        width: 200px;
        padding: 0 14px 0 0;
      }
      .right_tedian {
        margin-left: 200px;
      }
      .right_tedian_box {
        margin-left: 200px;
        width: auto;
      }
    }
  }
}
.right_tedian_box {
  margin-left: 28px;
  width: 100%;
}
.tedian_item {
  display: flex;
  align-items: start;
  margin-bottom: 15px;
}
.tedian_container {
  padding: 10px 10px 4px;
  border: 1px solid #dcdfe6;
  flex-wrap: wrap;
  background: #f7f7f7;
}
.tedian_itemChoose {
  font-size: 14px;
  color: #606266;
  line-height: normal;
  border-radius: 4px;
  padding: 6px 10px;
  margin-right: 6px;
  margin-bottom: 6px;
  border: 1px solid #dcdfe6;
  flex-shrink: 0;
  transition: all 0.3s;
  cursor: pointer;
}
.tedian_itemChoose.splitTitle {
  color: #333;
  border: 0;
  display: block;
  width: 100%;
  cursor: default;
  font-weight: 700;
}
.tedian_itemChoose.splitTitle:hover {
  background: none;
  color: #333;
}
.tedian_Choose_small {
  font-size: 13px;
  color: #606266;
  line-height: normal;
  border-radius: 4px;
  padding: 4px 6px;
  margin-right: 6px;
  margin-bottom: 6px;
  border: 1px solid #dcdfe6;
  flex-shrink: 0;
  margin-top: 6px;
  margin-right: 6px;
}
.tedian_Choose_small.active,
.tedian_itemChoose:hover,
.tedian_itemChoose.active {
  color: #ff6716;
  border-color: #ff6716;
  background: #fff;
}
.right_tedian .el-collapse-item__arrow {
  margin-left: -34px !important;
  margin-top: 0px !important;
  font-size: 22px;
  z-index: 1;
}
.searchIpt_done {
  width: 100%;
  border-radius: 4px;
  min-height: 40px;
  line-height: 40px;
  padding: 0 10px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
}
.down_arror_tedian,
.up_arror_tedian {
  font-size: 30px;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 3px;
}
.searchList_wrap {
  display: block;
  width: 100%;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
  padding-bottom: 4px;
}
.addChoose {
  display: inline-block;
  padding: 0px 20px;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
  background: #ff6716;
}
.searchNoDate {
  display: block;
  width: 100%;
  padding-bottom: 8px;
}
.icon_tedian {
  width: 18px;
  height: 18px;
  margin-left: 4px;
}
.tedian_Choose_small .icon_tedian {
  width: 14px;
  height: 14px;
}
</style>
