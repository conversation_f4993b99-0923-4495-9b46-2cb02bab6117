<template>
  <div class="recordCard">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick" stretch>
      <el-tab-pane
        v-for="(item, index) in tablist"
        :key="index"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
    </el-tabs>

    <div v-if="activeName == 'serviceRecord'">
      <el-tabs
        v-model="activeNameServe"
        stretch
        @tab-click="changeActiveNameServe"
      >
        <el-tab-pane label="小记" name="record"></el-tab-pane>
        <el-tab-pane label="砍价记录" name="assure"></el-tab-pane>
      </el-tabs>
      <div v-if="activeNameServe == 'record'">
        <div class="spaceBetween search_box">
          <el-input
            class="input-with-select"
            v-model="sn"
            placeholder="输入编号"
          >
            <span slot="prepend" value="1">商品</span>
            <!-- <el-select
              @change="changeSelect"
              v-model="select"
              slot="prepend"
              placeholder="请选择"
            >
              <el-option label="商品" value="1"></el-option>
              <el-option label="订单" value="2"></el-option>
            </el-select> -->
            <el-button @click="doSearch" slot="append" type="primary"
              >查询</el-button
            >
          </el-input>
        </div>
        <div>
          <el-input v-model="keyword" placeholder="输入搜索关键字"></el-input>
        </div>
        <div v-if="select == 1">
          <div v-if="list && list.length" class="spaceBetween record_box">
            <div v-for="(item, index) in list" :key="index" class="record">
              <div class="spaceBetween">
                <div>{{ util.getAppellation(item.consultFrom) }}</div>
                <div class="content">
                  {{ util.timeFormat(item.createTime, 'YYYY-MM-DD HH:mm') }}
                </div>
              </div>
              <div class="spaceBetween">
                <div class="content">{{ item.consultContent }}</div>
              </div>
            </div>
          </div>
          <div class="empty" v-else>{{ empty }}</div>
        </div>
        <div v-else>
          <div v-if="orderDetail.id" class="order_box">
            <orderCard :orderDetail="orderDetail"></orderCard>
            <div class="spaceBetween back_box">
              <el-input v-model="reason" placeholder="退款原因">
                <el-select
                  v-model="selectback"
                  slot="prepend"
                  placeholder="请选择"
                >
                  <el-option label="有责" value="1"></el-option>
                  <el-option label="无责" value="0"></el-option>
                </el-select>
              </el-input>
              <el-button class="backbtn" type="primary" @click="payBack"
                >申请退款</el-button
              >
            </div>
            <single-upload v-model="proofPics" />
          </div>
          <div class="empty" v-else>{{ empty }}</div>
        </div>
      </div>
      <div v-else>
        <div class="spaceBetween search_box">
          <el-input
            class="input-with-select"
            v-model="productSn"
            placeholder="输入编号"
          >
            <div slot="prepend">商品</div>
            <el-button @click="doSearch2" slot="append" type="primary"
              >查询</el-button
            >
          </el-input>
        </div>
        <div>
          <div v-for="item in listNegoProduct">
            <div class="assureitem">
              <div class="spaceBetween">
                <div>{{ item.productCategoryName }}</div>
                <div>{{ item.productSn }}</div>
                <div>
                  {{ util.timeFormat(item.createTime, 'YYYY-MM-DD HH:mm') }}
                </div>
              </div>
              <div class="spaceBetween">
                <div>
                  原价:<span class="price_red">{{ item.originPrice }}</span>
                </div>
                <div>
                  <span class="imspan" @click="goChat(item.buyerUsername)">{{
                    util.safephone(item.buyerUsername)
                  }}</span
                  >出价:<span class="price_red">{{ item.offerPrice }}</span>
                </div>
                <div>
                  <span class="price_red">{{ getStatus(item.status) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="activeName == 'userFooter'">
      <div>
        <div class="userFooter-top">
          <div class="spaceBetween member_box">
            <div class="spaceBetween member">
              <div class="label">手机号：</div>
              <div>{{ getPhone(member.phone) }}</div>
            </div>
            <div class="spaceBetween member">
              <div class="label">聊天号：</div>
              <div class="blue_txt" @click="goDialog">
                {{ member.imaccount }}
              </div>
            </div>
            <div class="spaceBetween member">
              <div class="label">是否实名：</div>
              <div class="red_txt">{{ getState(member.realNameConfirm) }}</div>
            </div>
            <div class="spaceBetween member">
              <div class="label">是否人脸认证：</div>
              <div class="red_txt">
                {{ getState(member.realPersionConfirm) }}
              </div>
            </div>
            <div class="spaceBetween member">
              <div class="label">是否包赔认证：</div>
              <div class="red_txt">{{ getState(member.baopeiConfirm) }}</div>
            </div>
            <div class="spaceBetween member">
              <div class="label">注册时间：</div>
              <div>
                {{ util.timeFormat(member.createTime, 'YYYY-MM-DD') }}
              </div>
            </div>
          </div>
        </div>
        <el-tabs
          class="userFooter"
          v-model="activeName2"
          stretch
          @tab-click="changeActiveName2"
        >
          <el-tab-pane label="咨询" name="consultbox"></el-tab-pane>
          <el-tab-pane label="议价" name="assurebox"></el-tab-pane>
          <el-tab-pane label="足迹" name="footer"></el-tab-pane>
          <el-tab-pane label="用户群" name="userteam"></el-tab-pane>
          <!-- <el-tab-pane label="足迹" name="footer"></el-tab-pane>
          <el-tab-pane label="发起的议价" name="assure"></el-tab-pane>
          <el-tab-pane label="收到的议价" name="assure2"></el-tab-pane>
          <el-tab-pane label="用户群" name="userteam"></el-tab-pane> -->
        </el-tabs>

        <!-- <div v-if="activeName2 == 'consultbox'">
          <el-tabs
            v-model="consultActiveName"
            @tab-click="changeConsultBox"
            type="card"
            :stretch="true"
          >
            <el-tab-pane label="收到的咨询" name="consultone"></el-tab-pane>
            <el-tab-pane label="发起的咨询" name="consulttwo"></el-tab-pane>
          </el-tabs>
          <div>
            123
          </div>
        </div> -->

        <div v-if="activeName2 == 'assurebox'">
          <el-tabs
            v-model="assureActiveName"
            @tab-click="changeAssureBox"
            type="card"
            :stretch="true"
          >
            <el-tab-pane label="发起的议价" name="assureone"></el-tab-pane>
            <el-tab-pane label="收到的议价" name="assuretwo"></el-tab-pane>
          </el-tabs>
          <div v-if="assureActiveName == 'assureone'">
            <div v-for="item in listNego">
              <div class="assureitem">
                <div class="spaceBetween">
                  <div>{{ item.productCategoryName }}</div>

                  <el-tooltip
                    v-if="item.logs"
                    class="item"
                    effect="dark"
                    placement="top-start"
                  >
                    <div slot="content">
                      <div v-html="item.logs"></div>
                    </div>
                    <div class="imspan" @click="goChat(item.sellerUsername)">
                      {{ item.productSn }}
                    </div>
                  </el-tooltip>
                  <div
                    v-else
                    class="imspan"
                    @click="goChat(item.sellerUsername)"
                  >
                    {{ item.productSn }}
                  </div>
                  <div>
                    {{ util.timeFormat(item.createTime, 'YYYY-MM-DD HH:mm') }}
                  </div>
                </div>
                <div class="spaceBetween">
                  <div>
                    原价:<span class="price_red">{{ item.originPrice }}</span>
                  </div>
                  <div>
                    {{ util.safephone(item.buyerUsername) }}出价:<span
                      class="price_red"
                      >{{ item.offerPrice }}</span
                    >
                  </div>
                  <div>
                    <span class="price_red">{{ getStatus(item.status) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="assureActiveName == 'assuretwo'">
            <div v-for="item in listNego2">
              <div class="assureitem">
                <div class="spaceBetween">
                  <div>{{ item.productCategoryName }}</div>
                  <div>
                    {{ item.productSn }}
                  </div>
                  <div>
                    {{ util.timeFormat(item.createTime, 'YYYY-MM-DD HH:mm') }}
                  </div>
                </div>
                <div class="spaceBetween">
                  <div>
                    原价:<span class="price_red">{{ item.originPrice }}</span>
                  </div>
                  <div>
                    <el-tooltip
                      v-if="item.logs"
                      class="item"
                      effect="dark"
                      placement="top-start"
                    >
                      <div slot="content">
                        <div v-html="item.logs"></div>
                      </div>
                      <div class="imspan" @click="goChat(item.buyerUsername)">
                        {{ util.safephone(item.buyerUsername) }}
                      </div>
                    </el-tooltip>
                    <div
                      v-else
                      class="imspan"
                      @click="goChat(item.buyerUsername)"
                    >
                      {{ util.safephone(item.buyerUsername) }}
                    </div>
                    出价:<span class="price_red">{{ item.offerPrice }}</span>
                  </div>
                  <div>
                    <span class="price_red">{{ getStatus(item.status) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="activeName2 == 'footer'">
          <div
            class="collect_item"
            v-for="(item, index) in footerList"
            :key="index"
          >
            <div class="collect_item_pic">
              <el-image :src="item.pic" class="productPic" />
            </div>
            <div class="collect_item_right">
              <div class="spaceBetween">
                <div class="collect_tit" @click="goProduct(item)">
                  <div class="collect_tit_two">{{ item.subTitle }}</div>
                  <div class="collect_tit_two">
                    {{ item.productCategoryName }}
                  </div>
                </div>
              </div>
              <div class="collect_price spaceBetween">
                <div class="list-price-new">¥ {{ item.price }}</div>
                <el-button @click="sendToIm(item)">发送商品</el-button>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="activeName2 == 'userteam'">
          <div>
            <div class="teamitem" v-for="item in userTeamList">
              <div>{{ util.timeFormat(item.createTime) }}</div>
              <el-tooltip
                v-if="item.teamNote"
                class="item"
                effect="dark"
                placement="top-start"
              >
                <div slot="content">
                  <div v-html="item.teamNote"></div>
                </div>
                <div class="imspan" @click="goTeam(item)">
                  <i
                    v-if="item.flowim == myAccount"
                    class="el-icon-star-on"
                  ></i>
                  {{ item.teamName }}
                </div>
              </el-tooltip>
              <div v-else class="imspan" @click="goTeam(item)">
                <i v-if="item.flowim == myAccount" class="el-icon-star-on"></i>
                {{ item.teamName }}
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="activeName2 == 'consultbox'">
          <div
            class="collect_item"
            v-for="(item, index) in consultList"
            :key="index"
          >
            <div class="collect_item_pic">
              <el-image :src="item.pic" class="productPic" />
            </div>
            <div class="collect_item_right">
              <div class="spaceBetween">
                <div class="collect_tit" @click="goProduct(item)">
                  <div class="collect_tit_two">{{ item.subTitle }}</div>
                  <div class="collect_tit_two">
                    {{ item.productCategoryName }}
                  </div>
                </div>
              </div>
              <div class="collect_price spaceBetween">
                <div class="list-price-new">¥ {{ item.price }}</div>
                <el-button @click="sendToIm(item)">发送商品</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="activeName == 'tips'">
      <div v-for="(item, index) in recordList" :key="index">
        <div class="assureitem">
          <div class="spaceBetween">
            <div>{{ util.getAppellation(item.consultFrom) }}</div>
            <div>{{ util.timeFormat(item.createTime) }}</div>
          </div>
          <div class="spaceBetween">
            <pre class="precnt">{{ item.consultContent }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
import SingleUpload from '@/components/Upload/singleUpload';

import {
  productConsultList,
  orderQueryBySn,
  returnApplyCreate,
  listMemberReadHis,
  memberConsultList,
  loadByIM,
  listNegotiations,
  getMemberByUsername,
  getIMTeamListByUsername,
  memberSendProductList
} from '@/api/kf.js';
import orderCard from './orderCard.vue';
import { inTeam } from '@/api/imteamManage';
const STATUSMAP = {
  '-1': '待支付意向金',
  '0': '买家已报价，待卖家回复',
  '1': '接受，待买家付款',
  '2': '拒绝，待重新报价或取消',
  '3': '已取消',
  '4': '全款订单已付',
  '5': '卖家拒绝并拉黑',
  '6': '已退款'
};
export default {
  components: {
    orderCard,
    SingleUpload
  },
  data() {
    return {
      consultActiveName: 'consultone',
      assureActiveName: 'assureone',
      member: {},
      userTeamList: [],
      listNegoProduct: [],
      util,
      listNego: [],
      listNego2: [],
      productSn: '',
      recordList: [],
      activeNameServe: 'record',
      activeName: 'userFooter',
      tablist: [
        {
          label: '用户信息',
          name: 'userFooter'
        },
        {
          label: '商品信息',
          name: 'serviceRecord'
        },
        {
          label: '用户备注',
          name: 'tips'
        }
      ],
      activeName2: 'consultbox',
      footerList: [],
      keyword: '',
      proofPics: '',
      selectback: '',
      reason: '',
      util,
      sn: '',
      list: [],
      select: '1',
      orderDetail: {},
      empty: '',
      username: '',
      consultList: []
    };
  },
  computed: {
    myAccount() {
      const { store } = window.__xkit_store__;
      return store.userStore.myUserInfo.account;
    }
  },
  mounted() {
    this.getfoot();
    this.getMember();
    this.getConsult();
  },
  methods: {
    getConsult() {
      const { store } = window.__xkit_store__;
      const memberIM = store.uiStore.selectedSession.replace('p2p-', '');
      let data = {
        memberIM
      };
      memberSendProductList(data).then(res => {
        this.consultList = res.data || [];
        this.consultList.reverse();
      });
    },
    changeConsultBox() {
      if (this.consultActiveName == 'consultone') {
      } else if (this.consultActiveName == 'consulttwo') {
      }
    },
    changeAssureBox() {
      if (this.assureActiveName == 'assureone') {
        this.getAssure();
      } else if (this.assureActiveName == 'assuretwo') {
        this.getAssure2();
      }
    },
    getStepTime(subSteps) {
      let keys = Object.keys(subSteps);
      let key = keys[0] || '';
      let time = subSteps[key].startTime || '';
      return time;
    },
    goDialog() {
      const { store, nim } = window.__xkit_store__;
      const sessionId = `p2p-${this.member.imaccount}`;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', this.member.imaccount);
      }
    },
    getState(state) {
      switch (state) {
        case 1:
          return '是';
        case 0:
          return '否';
        default:
          return '否';
      }
    },
    getMember() {
      const { store } = window.__xkit_store__;
      const im = store.uiStore.selectedSession.replace('p2p-', '');
      loadByIM({ im }).then(res => {
        if (res.code == 200) {
          this.username = res.data.username;
          this.member = res.data;
        }
      });
    },
    getPhone(phone) {
      if (phone) {
        return phone.replace(/\d{7}/, '*******');
      }
    },
    goChat(username) {
      getMemberByUsername({
        username
      }).then(res => {
        if (res.code == 200) {
          const { imaccount } = res.data;
          const { store } = window.__xkit_store__;
          const sessionId = `p2p-${imaccount}`;
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive('p2p', imaccount);
          }
        }
      });
    },
    getStatus(status) {
      return STATUSMAP[status];
    },
    changeActiveNameServe() {
      // if (this.activeNameServe == 'record') {
      // } else {
      // }
    },
    getUserTeam() {
      getIMTeamListByUsername({
        userName: this.username
      }).then(res => {
        if (res.code == 200) {
          this.userTeamList = res.data || [];
        }
      });
    },
    changeActiveName2() {
      if (this.activeName2 == 'footer') {
        this.getfoot();
      } else if (this.activeName2 == 'userteam') {
        this.getUserTeam();
      } else if (this.activeName2 == 'assurebox') {
        this.getAssure();
      } else if (this.activeName2 == 'consultbox') {
        this.getConsult();
      }
    },
    getAssure() {
      const { nim, store } = window.__xkit_store__;
      const session = store.uiStore.selectedSession;
      const im = session.replace('p2p-', '');
      listNegotiations({
        memberIM: im,
        page: 1,
        size: 999,
        negotiationRole: 1
      }).then(res => {
        if (res.code == 200) {
          this.listNego = res.data.list || [];
          this.listNego.forEach(ele => {
            if (ele.logs) {
              let logs = JSON.parse(ele.logs);
              if (logs.length) {
                ele.logs = logs.join('<br />');
              } else {
                ele.logs = '';
              }
            }
          });
        }
      });
    },
    getAssure2() {
      const { nim, store } = window.__xkit_store__;
      const session = store.uiStore.selectedSession;
      const im = session.replace('p2p-', '');
      listNegotiations({
        memberIM: im,
        page: 1,
        size: 999,
        negotiationRole: 0
      }).then(res => {
        if (res.code == 200) {
          this.listNego2 = res.data.list || [];
          this.listNego2.forEach(ele => {
            if (ele.logs) {
              let logs = JSON.parse(ele.logs);
              if (logs.length) {
                ele.logs = logs.join('<br />');
              } else {
                ele.logs = '';
              }
            }
          });
        }
      });
    },
    goTeam(item) {
      if (item.flowim !== this.myAccount) {
        const { teamId } = item;
        this.$confirm('你当前不在这个群内，要进入群聊吗', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
          distinguishCancelAndClose: true //区分取消与关闭
        }).then(() => {
          inTeam(teamId).then(res => {
            if (res.code == 200) {
              const imaccount = teamId;
              const { store } = window.__xkit_store__;
              const sessionId = `team-${imaccount}`;
              if (store.sessionStore.sessions.get(sessionId)) {
                store.uiStore.selectSession(sessionId);
              } else {
                store.sessionStore.insertSessionActive('team', imaccount);
              }
            }
          });
        });
      } else {
        this.$confirm('要进入群聊吗', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning',
          distinguishCancelAndClose: true //区分取消与关闭
        }).then(() => {
          const { teamId } = item;
          const imaccount = teamId;
          const { store } = window.__xkit_store__;
          const sessionId = `team-${imaccount}`;
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive('team', imaccount);
          }
        });
      }
    },
    getTeamName(item) {
      const { teamId } = item;
      const { nim, store } = window.__xkit_store__;
      const team = store.teamStore.teams.get(teamId);
      if (team) {
        return team.name;
      } else {
        return '';
      }
    },
    getfoot() {
      const { nim, store } = window.__xkit_store__;
      const session = store.uiStore.selectedSession;
      const im = session.replace('p2p-', '');
      this.listMemberReadHis(im);
    },
    handleClick() {
      if (this.activeName == 'tips') {
        const { store } = window.__xkit_store__;
        const im = store.uiStore.selectedSession.replace('p2p-', '');
        loadByIM({ im }).then(res => {
          if (res.code == 200) {
            const username = res.data.username;
            memberConsultList({
              username
            }).then(response => {
              if (response.code == 200) {
                this.recordList = response.data;
              }
            });
          }
        });
      } else if (this.activeName == 'userFooter') {
        this.getfoot();
      } else {
        this.getMember();
      }
    },
    sendToIm(item) {
      const { store, nim } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      const splitList = sessionId.split('-');
      const scene = splitList[0];
      splitList.shift();
      const to = splitList.join('-');
      const myAccount = store.userStore.myUserInfo.account;
      const productDetail = item;
      const { productSn, productCategoryId } = productDetail;
      const content = `<div class="spaceBetween msg-flexstart">
      <img src="${productDetail.pic}" class="msg-productImg" />
      <div>
        <div class="twoLine">${productDetail.subTitle.substring(0, 30)}</div>
        <div class="msg-red">￥${productDetail.price || ''}</div>
      </div>
    </div>`;
      const attach = {
        data: {
          type: 'product',
          productSn,
          productId: productDetail.id,
          productCategoryId
        },
        body: {
          title: productDetail.subTitle.substring(0, 30),
          content
        },
        type: 'kk_product_msg_fed'
      };
      store.msgStore
        .sendCustomMsgActive({
          scene: scene,
          from: myAccount,
          to: to,
          attach: JSON.stringify(attach)
        })
        .then(res => {
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        })
        .catch(err => {
          console.log('发送失败', err);
        });
    },
    goProduct(item) {
      window.open(`https://www.kkzhw.com/playDetail?productId=${item.id}`);
    },
    listMemberReadHis(im) {
      listMemberReadHis({
        memberIM: im
      }).then(res => {
        if (res.code == 200) {
          this.footerList = res.data || [];
        }
      });
    },
    doSearch2() {
      if (!this.productSn) {
        this.$message.error('请输入商品编号');
        return;
      }
      listNegotiations({
        productSn: this.productSn,
        page: 1,
        size: 999
      }).then(res => {
        if (res.code == 200) {
          this.listNegoProduct = res.data.list;
        }
      });
    },
    doSearchAuto(sn, type) {
      this.clear();
      this.select = `${type}`;
      this.sn = sn;
      this.doSearch();
    },
    payBack() {
      returnApplyCreate({
        orderId: this.orderDetail.id,
        reason: this.reason,
        type: this.selectback,
        proofPics: this.proofPics
      }).then(res => {
        if (res.code === 200) {
          this.$message.success('申请退款成功');
        }
      });
    },
    clear() {
      this.proofPics = '';
      this.selectback = '';
      this.empty = '';
      this.list = [];
      this.orderDetail = {};
    },
    changeSelect() {
      this.clear();
    },
    doSearch() {
      if (!this.sn) {
        this.$message.error('请输入商品编号');
        return;
      }
      if (this.select === '2') {
        orderQueryBySn({
          orderSn: this.sn
        }).then(res => {
          if (res.data) {
            this.orderDetail = res.data;
          } else {
            this.orderDetail = {};
            this.empty = '暂无结果';
          }
        });
      } else {
        let data = {
          productSn: this.sn
        };
        if (this.keyword) {
          data.keyword = this.keyword;
        }
        productConsultList(data).then(res => {
          this.list = res.data;
          if (this.list.length === 0) {
            this.empty = '暂无结果';
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.userFooter-top {
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}
.member_box {
  flex-wrap: wrap;
  font-size: 12px;
}
.refresh {
  color: #3995fb;
  cursor: pointer;
}
.member {
  width: 48%;
  line-height: 30px;
  .red_txt {
    color: #fe5a1e;
  }
  .blue_txt {
    color: #3995fb;
    cursor: pointer;
  }
  .label {
    color: #666;
  }
}
.userFooter {
  /deep/ .el-tabs__item {
    padding: 0 5px 0 0;
  }
}
.teamitem {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
.imspan {
  color: #409eff;
  cursor: pointer;
  display: inline-block;
}
.assureitem {
  margin: 10px 0;
  font-size: 12px;
  border-bottom: 1px solid #ccc;
  .spaceBetween {
    margin: 5px;
  }
  .price_red {
    color: #fe5a1e;
  }
}
.precnt {
  word-break: break-all;
  white-space: pre-wrap;
}
.userFooter {
  margin: 10px 0;
  text-align: center;
}
.collect_item {
  overflow: hidden;
  margin: 10px 0;
  border: 1px solid #ccc;
  padding: 15px;
  cursor: pointer;
}
.collect_item_pic {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 6px;
  float: left;
  .productPic {
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
}
.collect_item_pic > image {
  width: 100%;
  height: 100%;
}
.collect_item_right {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  padding-left: 90px;
}
.collect_item_top {
  font-size: 12px;
  color: #000;
  font-weight: 600;
}
.collect_tit {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  padding-top: 4px;
}
.collect_tit_two {
  font-size: 12px;
  color: #666;
  padding-top: 2px;
  width: 150px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  word-break: break-all;
}
.collect_price {
  font-weight: 700;
  font-size: 14px;
}
.list-price-new {
  color: #fe5a1e;
}
.empty {
  text-align: center;
  line-height: 40px;
}
.back_box {
  margin: 10px 0;
}
.backbtn {
  margin-left: 10px;
}
/deep/ .el-select .el-input {
  width: 100px;
}
/deep/ .input-with-select .el-input-group__prepend {
  background-color: #fff;
}
/deep/ .back_box .el-input-group__prepend {
  background-color: #fff;
}
.recordCard {
  padding: 20px;
  .searchIpt {
    margin-right: 20px;
  }
  .search_box {
    margin-top: 10px;
  }
  .order_box {
    position: relative;
    .spaceBetween {
      justify-content: space-evenly;
    }
  }
  .record_box {
    font-size: 12px;
    flex-wrap: wrap;
    align-items: center;
    .record {
      width: 100%;
      border: 1px solid #ccc;
      padding: 10px;
      margin-top: 10px;
      .label {
        width: 120px;
      }
      .spaceBetween {
        align-items: flex-start;
        line-height: 30px;
      }
      .content {
        color: #666;
        max-width: 400px;
        // 强制换行
        word-break: break-all;
        word-wrap: break-word;
      }
    }
  }
}
</style>
