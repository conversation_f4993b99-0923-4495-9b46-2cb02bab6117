<template>
  <div class="orderCard">
    <div class="spaceStart order">
      <img class="order_img" :src="productDetail.productPic" />
      <div class="order_content">
        <div class="content_item">
          订单编号：<span class="red_txt">{{ orderDetail.orderSn }}</span>
        </div>
        <div class="content_item">
          商品编号：<span class="red_txt">{{ productDetail.productSn }}</span>
        </div>
        <div class="content_item">
          创建时间：<span class="black_txt">
            {{
              util.timeFormat(orderDetail.createTime, 'YYYY-MM-DD HH:mm:ss')
            }}</span
          >
        </div>
        <div class="content_item">
          游戏名称：<span class="black_txt">{{
            orderDetail.productCategoryName
          }}</span>
        </div>
        <div class="content_item">
          订单类型：<span class="red_txt">{{ getOrderType() }}</span>
        </div>
        <div v-if="baopeiList.length === 0" class="content_item">
          <div>包赔类型：<span class="black_txt">无</span></div>
        </div>
        <div v-else>
          <div
            v-for="(item, index) in baopeiList"
            :key="index"
            class="content_item"
          >
            <div>
              {{ item.productAttr[0].key }}：<span class="black_txt">{{
                item.productName
              }}</span>
            </div>
          </div>
        </div>
        <div class="content_item">
          号价：<span class="black_txt"
            >￥{{ productDetail.productPrice }}</span
          >
        </div>
        <div class="content_item">
          支付金额：<span class="black_txt">￥{{ orderDetail.payAmount }}</span>
        </div>
        <div class="content_item">
          订单状态：<span class="black_txt">{{
            util.getStatus(orderDetail.status)
          }}</span>
        </div>
        <div class="content_item">
          卖家到手价：<span class="black_txt"
            >￥{{ orderDetail.sellerhandamount }}</span
          >
        </div>
        <div>
          <el-link type="primary" @click="showReturn">申请退款</el-link>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="returnDialog"
      :before-close="onCancel"
      width="40%"
      center
      title="申请退款"
    >
      <el-form
        ref="postForm"
        :label-width="formLabelWidth"
        :model="postForm"
        class="form-box"
        :rules="rules"
      >
        <div>
          <el-form-item label="剩余金额">
            <span class="red">¥ {{ balance }}元</span>
          </el-form-item>
          <el-form-item label="退款金额" prop="price">
            <el-input v-model="postForm.price" placeholder="退款金额">
            </el-input>
          </el-form-item>
          <el-form-item label="退款原因" prop="reason">
            <el-input v-model="postForm.reason" placeholder="退款原因">
            </el-input>
          </el-form-item>
          <el-form-item label="退款成功后自动上架" prop="reason">
            <el-radio-group v-model="postForm.publish">
            <el-radio :label="1" value="1">是</el-radio>
            <el-radio :label="0" value="0">否</el-radio>
          </el-radio-group>
          </el-form-item>
          <!-- <el-form-item>
            <single-upload v-model="postForm.proofPics" />
          </el-form-item> -->
        </div>
      </el-form>
      <div slot="footer" class="m-footer spaceEnd">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit('postForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryBalance } from '@/api/order';
import { returnApplyCreate } from '@/api/kf.js';
import util from '@/utils/index';
// import SingleUpload from '@/components/Upload/singleUpload';
export default {
  props: {
    orderDetail: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },

  computed: {
    productDetail() {
      if (this.orderDetail && this.orderDetail.orderItemList) {
        const findIt = this.orderDetail.orderItemList.find(ele => {
          return ele.itemType === 0;
        });
        return findIt || {};
      }
      return {};
    },
    baopeiList() {
      if (this.orderDetail && this.orderDetail.orderItemList) {
        const list = this.orderDetail.orderItemList.filter(ele => {
          let { productAttr, itemType } = ele;
          if (itemType == 1) {
            try {
              productAttr = JSON.parse(productAttr);
              ele.productAttr = productAttr;
            } catch (e) {
              console.log(e);
            }
          }
          return ele.itemType === 1;
        });
        return list;
      }
      return [];
    }
  },
  data() {
    return {
      util,
      rules: {
        price: [
          {
            required: true,
            message: '请输入金额',
            trigger: 'blur'
          }
        ],
        reason: [
          {
            required: true,
            message: '请输入原因',
            trigger: 'blur'
          }
        ]
      },
      balance: '',
      formLabelWidth: '150px',
      postForm: {
        reason: '',
        price: '',
        publish:0,
      },
      returnDialog: false
    };
  },
  methods: {
    onSubmit() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          const params = {
            returnAmount: this.postForm.price,
            orderId: this.orderDetail.id,
            reason: this.postForm.reason,
            publish:this.postForm.publish,
            type: 0
          };
          returnApplyCreate(params)
            .then(response => {
              if (response.code == 200) {
                this.$message({
                  type: 'success',
                  message: '申请退款成功!'
                });
              }
              this.postForm={
                reason: '',
                price: '',
                publish:0,
              }
            })
            .finally(() => {
              this.onCancel();
            });
        }
      });
    },
    onCancel() {
      this.postForm={
                reason: '',
                price: '',
                publish:0,
              }
      this.returnDialog = false;
    },
    showReturn() {
      queryBalance({
        orderId: this.orderDetail.id
      }).then(res => {
        if (res.code == 200) {
          this.returnDialog = true;
          this.balance = res.data.balance;
        }
      });
    },
    getOrderType() {
      return this.orderDetail.orderTypeName;
    }
  }
};
</script>
<style lang="scss" scoped>
.red {
  color: red;
}
.orderCard {
  .orderHistory {
    margin-right: 10px;
  }
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  .order {
    align-items: flex-start;
    .order_img {
      width: 100px;
      height: 100px;
      object-fit: cover;
      margin-right: 10px;
    }
    .order_content {
      color: #666;
      line-height: 18px;
      .content_item {
        margin-bottom: 10px;
        .red_txt {
          color: #fe5a1e;
        }
        .black_txt {
          color: #000;
        }
      }
    }
  }
}
</style>
