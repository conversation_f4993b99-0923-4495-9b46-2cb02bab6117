<template>
  <div class="actionCard">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick" stretch>
      <el-tab-pane
        v-for="(item, index) in tablist"
        :key="index"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
    </el-tabs>
    <div v-if="activeName === 'first'">
      <div v-if="!isEdit" class="card_content" v-html="teamNote"></div>
      <div v-else>
        <textarea
          style="width: 100%; padding: 5px; border: 1px solid #ccc"
          class="card_content"
          v-model="teamNote"
        ></textarea>
      </div>
      <el-button size="mini" v-show="isEdit" @click="cancel(0)">取消</el-button>
    </div>
    <div v-else-if="activeName === 'tips'">
      <div>
        <div v-for="item in teamRecordList">
          <div class="assureitem">
            <div class="spaceBetween">
              <div>{{ util.getAppellation(item.consultFrom) }}</div>
              <div>{{ util.timeFormat(item.createTime) }}</div>
            </div>
            <div class="spaceBetween">
              <pre class="precnt">{{ item.consultContent }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="activeName === 'fuwu'">
      <div v-if="!isEdit" class="card_content" v-html="teamPnote"></div>
      <div v-else>
        <textarea
          style="width: 100%; padding: 5px; border: 1px solid #ccc"
          class="card_content"
          v-model="teamPnote"
        ></textarea>
      </div>
      <el-button size="mini" type="primary" @click="toggleEdit(1)">{{
        editTxt
      }}</el-button>
      <el-button size="mini" v-show="isEdit" @click="cancel(1)">取消</el-button>
    </div>
    <div v-else>
      <div v-if="activeName == 'sale'">
        <el-button class="refresh" @click="doShenHe">包赔审核</el-button>
      </div>
      <div v-if="activeName == 'buy'" class="spaceBetween">
        <span class="refresh" @click="soundCall('buyer')">呼叫买家</span>
        <span class="refresh" @click="goDialog">私聊买家</span>
      </div>
      <div v-if="activeName == 'sale'" class="spaceBetween">
        <span class="refresh" @click="soundCall('seller')">呼叫卖家</span>
        <span class="refresh" @click="goDialog">私聊卖家</span>
      </div>
      <div class="spaceBetween member_box">
        <div class="spaceBetween member">
          <div class="label">手机号：</div>
          <div>{{ getPhone(member.phone) }}</div>
        </div>
        <div class="spaceBetween member">
          <div class="label">聊天号：</div>
          <div class="blue_txt" @click="goDialog">{{ member.imaccount }}</div>
        </div>
        <div class="spaceBetween member">
          <div class="label">是否实名：</div>
          <div class="red_txt">{{ getState(member.realNameConfirm) }}</div>
        </div>
        <div class="spaceBetween member">
          <div class="label">是否人脸认证：</div>
          <div class="red_txt">{{ getState(member.realPersionConfirm) }}</div>
        </div>
        <div class="spaceBetween member">
          <div class="label">是否包赔认证：</div>
          <div class="red_txt">{{ getState(member.baopeiConfirm) }}</div>
        </div>
        <div class="spaceBetween member">
          <div class="label">注册时间：</div>
          <div>
            {{ util.timeFormat(member.createTime, 'YYYY-MM-DD') }}
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="allocDialogVisible"
      title="审核"
      width="60%"
      top="1vh"
    >
      <el-form
        ref="shenheForm"
        :model="shenheForm"
        label-width="150px"
        style="display: flex; flex-wrap: wrap"
        size="small"
      >
        <el-form-item style="width: 100%" label="实名认证：">
          <el-radio-group v-model="shenheForm.defaultStatus">
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="1">无效</el-radio>
            <el-radio :label="2">有效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="width: 100%" label="人脸认证：">
          <el-radio-group v-model="shenheForm.faceStatus">
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="1">无效</el-radio>
            <el-radio :label="2">有效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="width: 100%" label="包赔认证：">
          <el-radio-group v-model="shenheForm.baopeiStatus">
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="1">无效</el-radio>
            <el-radio :label="2">有效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="width: 100%" label="用户名：">
          <el-input
            v-model="shenheForm.memberUserName"
            disabled
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item
          v-if="shenheForm.baopeiStatus === 1"
          style="width: 90%"
          label="包赔拒绝原因:"
        >
          <el-input v-model="shenheForm.region" />
        </el-form-item>
        <el-form-item style="width: 50%" label="真实姓名：">
          <el-input
            disabled
            v-model="shenheForm.userIdName"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item style="width: 50%" label="身份证号：">
          <el-input
            disabled
            v-model="shenheForm.userIdNumber"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item style="width: 50%" label="身份证正面：">
          <el-image alt="暂无图片" :src="shenheForm.userIdPic1" fit="fill">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-form-item>
        <el-form-item style="width: 50%" label="身份证反面：">
          <el-image alt="暂无图片" :src="shenheForm.userIdPic2" fit="fill">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-form-item>
        <el-form-item style="width: 50%" label="紧急联系人姓名：">
          <el-input disabled v-model="shenheForm.name" style="width: 200px" />
        </el-form-item>
        <el-form-item style="width: 50%" label="紧急联系人电话：">
          <el-input
            disabled
            v-model="shenheForm.phoneNumber"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item style="width: 100%" label="订单图片：">
          <el-image alt="暂无图片" :src="shenheForm.userOrderPic" fit="fill"
            ><div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="allocDialogVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="handleDialogConfirm2()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog width="30%" title="呼叫用户" :visible.sync="callDialog">
      <el-form :model="callDialogForm">
        <el-form-item label="呼叫类型">
          <el-select
            v-model="callDialogForm.callType"
            placeholder="请选择呼叫类型"
          >
            <el-option label="用户咨询呼叫" value="1833802"></el-option>
            <el-option label="买家下单呼叫" value="1423701"></el-option>
            <el-option label="审核呼叫" value="1484700"></el-option>
            <el-option label="呼叫买家，卖家有答复" value="2299446"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="callDialog = false">取 消</el-button>
        <el-button type="primary" @click="doCall">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchDetail,
  fetchListAll,
  addressAdd,
  addressUpStatus,
  addressUpdate,
} from '@/api/address';

import { memberManageAddress } from '@/api/kf.js';
const defaultAdmin = {
  name: '',
  phoneNumber: '',
  defaultStatus: '',
  faceStatus: '',
  baopeiStatus: '',
  memberUserName: '',
  region: '',
  userIdName: '',
  userIdNumber: '',
  userIdPic1: '',
  userIdPic2: '',
  userOrderPic: '',
  teamId: '',
  type: 3,
};
const TABLIST = [
  {
    label: '流程信息',
    name: 'first',
  },
  {
    label: '买家信息',
    name: 'buy',
  },
  {
    label: '卖家信息',
    name: 'sale',
  },
  {
    label: '服务小记',
    name: 'fuwu',
  },
];
import util from '@/utils/index';
import store from '../../store';
import {
  addNote,
  addServerNote,
  soundCall,
  teamConsultList,
} from '@/api/kf.js';
export default {
  name: 'actionCard',
  props: {
    flowId: {
      type: [String, Number],
      default: '',
    },
    sidim: {
      type: [String, Number],
      default: '',
    },
    bidim: {
      type: [String, Number],
      default: '',
    },
    teamNote: {
      type: String,
      default: undefined,
    },
    teamPnote: {
      type: String,
      default: undefined,
    },
    teamInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    hasFlow: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    tablist() {
      let list = [];
      TABLIST.forEach((ele, index) => {
        if (index === 0 && this.teamNote !== undefined) {
          list.push(ele);
        } else if (index === 1 && this.teamPNote !== undefined) {
          list.push(ele);
        } else if (index === 1 && this.bidim) {
          list.push(ele);
        } else if (index === 2 && this.sidim) {
          list.push(ele);
        }
      });
      if (list[0]) {
        this.activeName = list[0].name;
      }
      list.push({
        label: '群备注',
        name: 'tips',
      });
      return list;
    },
    editTxt() {
      return this.isEdit ? '确认' : '编辑';
    },
    member() {
      if (this.activeName === 'buy') {
        return store.getters.bidMember;
      } else if (this.activeName === 'sale') {
        return store.getters.sidMember;
      }
    },
  },
  data() {
    return {
      teamRecordList: [],
      memberRole: '',
      callDialog: false,
      callDialogForm: {
        callType: '',
        memberIM: '',
      },
      shenheForm: Object.assign({}, defaultAdmin),
      allocDialogVisible: false,
      util,
      activeName: 'first',
      isEdit: false,
      isNoteEdit: false,
    };
  },
  methods: {
    getTeamRecord() {
      teamConsultList({
        teamId: this.flowId,
      }).then((res) => {
        if (res.code == 200) {
          this.teamRecordList = res.data || [];
        }
      });
    },
    doCall() {
      const { memberIM, callType } = this.callDialogForm;
      const { store } = window.__xkit_store__;
      const selectedSession = store.uiStore.selectedSession;
      let data = {
        memberIM,
        taskId: callType,
        from: selectedSession.replace('team-', ''),
        memberRole: this.memberRole,
      };
      soundCall(data).then((res) => {
        if (res.code == 200) {
          this.$message.success('呼叫成功');
          this.callDialog = false;
        }
      });
    },
    soundCall(type) {
      this.$emit('actionCardClickChange')
      this.memberRole = type;
      const memberIM = this.member.imaccount;
      this.callDialogForm.memberIM = memberIM;
      this.callDialog = true;
    },
    handleDialogConfirm2() {
      this.$confirm('是否要确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        addressUpdate(this.shenheForm.id, this.shenheForm).then((response) => {
          if(response.code==200){
              this.$message({
              message: '修改成功！',
              type: 'success',
            });
            this.allocDialogVisible = false;
            this.$emit('doSendBaopeiMsg2Team');
          }
        });
      });
    },
    doShenHe() {
      memberManageAddress(this.member.id).then((res) => {
        if (res.code == 200) {
          this.shenheForm = Object.assign({}, res.data);
          this.allocDialogVisible = true;
        }
      });
    },
    doRefresh() {
      this.$emit('doRefresh');
    },
    getPhone(phone) {
      if (phone) {
        return phone.replace(/\d{7}/, '*******');
      }
    },
    toggleEdit(flag) {
      this.isEdit = !this.isEdit;
      let params = {
        id: this.teamInfo.id,
        // orderId: this.teamInfo.orderId
      };
      if (flag === 1) {
        if (!this.isEdit) {
          let data = this.teamPnote;
          addServerNote(data, params);
        } else {
          this.textSave = this.teamPnote;
        }
      } else {
        if (!this.isEdit) {
          let data = this.teamNote;
          addNote(data, params);
        } else {
          this.textSave = this.teamNote;
        }
      }
    },
    cancel(flag) {
      this.isEdit = false;
      if (flag === 1) {
        this.teamPnote = this.textSave;
      } else {
        this.teamNote = this.textSave;
      }
    },
    goDialog() {
      
      const { store, nim } = window.__xkit_store__;
      const sessionId = `p2p-${this.member.imaccount}`;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', this.member.imaccount);
      }
      this.$emit('actionCardClickChange')
    },
    getState(state) {
      switch (state) {
        case 1:
          return '是';
        case 0:
          return '否';
        default:
          return '否';
      }
    },
    handleClick() {
      this.isEdit = false;
      this.doRefresh();
      if (this.activeName == 'tips') {
        this.getTeamRecord();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.teamitem {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
.imspan {
  color: #409eff;
  cursor: pointer;
}
.assureitem {
  margin: 10px 0;
  font-size: 12px;
  border-bottom: 1px solid #ccc;
  .spaceBetween {
    margin: 5px;
  }
  .price_red {
    color: #fe5a1e;
  }
}
.member_box {
  flex-wrap: wrap;
  font-size: 12px;
}
.refresh {
  color: #3995fb;
  cursor: pointer;
}
.member {
  width: 48%;
  line-height: 30px;
  .red_txt {
    color: #fe5a1e;
  }
  .blue_txt {
    color: #3995fb;
    cursor: pointer;
  }
  .label {
    color: #666;
  }
}
.member2 {
  width: 100%;
}
.actionCard {
  /deep/ .el-tabs__item {
    padding: 0 !important;
  }
  padding: 15px;
  border-bottom: 1px solid #ccc;
  .card_title {
    font-size: 16px;
  }
  .card_content {
    margin: 5px 0;
    flex-wrap: wrap;
    height: 170px;
    width: 310px;
    overflow: auto;
    font-size: 12px;
    word-break: normal;
    white-space: nowrap;
  }
  .card_button {
    .btn_item {
      border: 1px solid #e5e5e5;
      padding: 3px 20px;
      margin-left: 15px;
      cursor: pointer;
    }
    .btn_item_primary {
      background-color: #fe5a1e;
      color: #fff;
    }
  }
}
</style>
