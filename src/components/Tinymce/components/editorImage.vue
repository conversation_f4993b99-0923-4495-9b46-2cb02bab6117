<template>
  <div class="upload-container">
    <el-button
      :style="{ background: color, borderColor: color }"
      icon="el-icon-upload"
      size="mini"
      type="primary"
      @click="dialogVisible = true"
      >上传图片
    </el-button>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <el-upload
        ref="upload"
        :action="useOss ? ossUploadUrl : minioUploadUrl"
        :data="useOss ? dataObj : null"
        :multiple="true"
        :file-list="fileList"
        :show-file-list="true"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        list-type="picture-card"
        accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </el-dialog>
  </div>
</template>

<script>
import { policy } from '@/api/oss';
const webkk = process.env.BASE_API;
export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#1890ff'
    }
  },
  data() {
    return {
      dialogVisible: false,
      listObj: {},
      fileList: [],
      dataObj: {
        policy: '',
        signature: '',
        key: '',
        ossaccessKeyId: '',
        dir: '',
        host: ''
      },
      useOss: true, // 使用oss->true;使用MinIO->false
      ossUploadUrl: 'https://images2.kkzhw.com',
      minioUploadUrl: `${webkk}/mall-admin/minio/upload`
    };
  },
  methods: {
    checkAllSuccess() {
      return Object.keys(this.listObj).every(
        item => this.listObj[item].hasSuccess
      );
    },
    handleSubmit() {
      const arr = Object.keys(this.listObj).map(v => this.listObj[v]);
      if (!this.checkAllSuccess()) {
        this.$message(
          '请等待所有图片上传成功 或 出现了网络问题，请刷新页面重新上传！'
        );
        return;
      }
      console.log(arr);
      this.$emit('successCBK', arr);
      this.listObj = {};
      this.fileList = [];
      this.dialogVisible = false;
    },
    handleSuccess(response, file) {
      const uid = file.uid;
      const objKeyArr = Object.keys(this.listObj);
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          this.listObj[objKeyArr[i]].url =
            this.dataObj.host + '/' + this.dataObj.key;
          if (!this.useOss) {
            // 不使用oss直接获取图片路径
            this.listObj[objKeyArr[i]].url = response.data.url;
          }
          this.listObj[objKeyArr[i]].hasSuccess = true;
          return;
        }
      }
    },
    handleRemove(file) {
      const uid = file.uid;
      const objKeyArr = Object.keys(this.listObj);
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          delete this.listObj[objKeyArr[i]];
          return;
        }
      }
    },
    rename(file, fineName) {
      // const timeStamp = new Date().getTime();
      // const name = `${timeStamp}_${file.name}`;
      const copyFile = new File([file], fineName, {
        type: file.type
      });
      copyFile.uid = file.uid;
      const index = this.$refs.upload.uploadFiles.findIndex(ele => {
        return ele.uid === file.uid;
      });
      this.$refs.upload.uploadFiles[index].raw = copyFile;
      this.$refs.upload.uploadFiles[index].name = copyFile.name;
      this.$refs.upload.uploadFiles[index].url = URL.createObjectURL(copyFile);
      return copyFile;
    },
    beforeUpload(file) {
      const _self = this;
      const fileName = file.uid;
      this.listObj[fileName] = {};
      if (!this.useOss) {
        // 不使用oss不需要获取策略
        this.listObj[fileName] = {
          hasSuccess: false,
          uid: file.uid,
          width: this.width,
          height: this.height
        };
        return true;
      }
      return new Promise((resolve, reject) => {
        policy()
          .then(response => {
            const ext = file.name.split('.').pop();
            const fname = response.data.fileName;
            _self.dataObj.policy = response.data.policy;
            _self.dataObj.signature = response.data.signature;
            _self.dataObj.ossaccessKeyId = response.data.accessKeyId;
            _self.dataObj.key = response.data.dir + `/${fname}.${ext}`;
            _self.dataObj.dir = response.data.dir;
            _self.dataObj.host = response.data.host;
            _self.listObj[fileName] = {
              hasSuccess: false,
              uid: file.uid,
              width: this.width,
              height: this.height
            };
            const copyFile = this.rename(file, `${fileName}.${ext}`);
            // _self.dataObj.callback = response.data.callback;
            resolve(copyFile);
          })
          .catch(err => {
            console.log(err);
            reject(false);
          });
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.upload-container .editor-slide-upload {
  margin-bottom: 20px;
}
</style>
