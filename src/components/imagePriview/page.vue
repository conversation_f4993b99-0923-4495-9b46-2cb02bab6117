<template>
    <div>
      <swperImagePriview
        v-if="showViewer"
        :tableData="tableData"
        :product="productObj"
        :productSn="productSn"
        :z-index="10000"
        :initial-index="imgViewer"
        :on-close="closeViewer"
        :url-list="arrDtPicForShow"
        :tableDataFlag="true"
        :gameSysinfoReadcount="gameSysinfoReadcount"
        :gameSysinfoCollectcount="gameSysinfoCollectcount"
        :price="price"
      />
    </div>
  </template>
  <script>
  import swperImagePriview from './index.vue';
  import defaultImage from '../../assets/images/sl_img.png';
  import {
    getDetailByCode
  } from '@/api/bmc';
  export default {
    components: {
      swperImagePriview,
    },
    data() {
      return {
        tableData: [],
        productObj: {},
        productSn: '',
        showViewer: false,
        imgViewer: 0,
        arrDtPicForShow: [],
        gameSysinfoReadcount: 0,
        gameSysinfoCollectcount: 0,
        price: 0,
        gameSysinfoReadcount: '',
        gameSysinfoCollectcount: '',
        checkBoxAttributeList:[],
      };
    },
    mounted() {},
    methods: {
      closeViewer() {
        this.showViewer = false;
      },
      getSarchArr(productAttributeList) {
        let queryIntParams = undefined;
        console.log(this.checkBoxAttributeList,11122222);
        
        const checkBoxParams = this.checkBoxAttributeList.map((e) => {
          console.log(e,111111);
          
          return {
            ...e,
            value: e.value,
          };
        });
        const findIndex = checkBoxParams.findIndex((ele) => {
          return ele.sort === 32306 && ele.ename;
        });
        let queryStrParams = [];
        if (findIndex !== -1) {
          let { value, ename } = checkBoxParams[findIndex];
          if (value) {
            queryStrParams.push({
              key: ename,
              value,
            });
          }
          checkBoxParams.splice(findIndex, 1);
        }
        const inputParams =productAttributeList
          .map((e) => {
            let [min, max] = e.selectValue || [];
            // 价格是公共属性，得放到queryIntParams
            if (e.name === '价格' && (!isNaN(min) || !isNaN(max))) {
              queryIntParams = [
                {
                  min: !isNaN(min) ? parseInt(min, 10) : undefined,
                  key: 'price',
                  max: !isNaN(max) ? parseInt(max, 10) : undefined,
                },
              ];
            }
            return {
              ...e,
              value:
                !isNaN(min) || !isNaN(max) ? `${min || 0}-${max || 9999999}` : '',
            };
          })
          .filter((e) => e.name !== '价格');
        return checkBoxParams.concat(inputParams);
      },
      formatValue(value) {
        return value
          .replace(/[,]/g, '，')
          .replace(/\[核\]/g, '')
          .replace(/\[绝\]/g, '')
          .replace(/\[钱\]/g, '');
      },
      mergeOptions(productAttributeList, productAttributeValueList) {
        productAttributeList.sort((a, b) => {
          return a.type - b.type;
        });
        productAttributeList.sort((a, b) => {
          return b.sort - a.sort;
        });
        let tempList = [];
        productAttributeList.forEach((ele) => {
          if (ele.name == '营地ID') {
            const findIt = productAttributeValueList.find((item) => {
              return item.productAttributeId === ele.id;
            });
            this.wzryId = findIt && findIt.value;
          }
          if (ele.type === 1 || ele.type === 2) {
            const findV = productAttributeValueList.find((item) => {
              return item.productAttributeId === ele.id;
            });
            if (findV && findV.value) {
              tempList.push({
                name:
                  ele.selectType == 2
                    ? `【${ele.name}】${this.formatValue(findV.value)}`
                    : ele.name,
                label: ele.name,
                value: this.formatValue(findV.value),
                sort: ele.sort,
                selectType: ele.selectType,
              });
            }
          }
        });
        return tempList;
      },
      showImagePriview(item,arr) {
        console.log(item, 1111111);
  
        getDetailByCode({ productSn: item.productSn }).then((res) => {
          
          this.checkBoxAttributeList = res.data.productAttributeList;
          const product = res.data.product;
          if(product.publishStatus==-2){
            this.$message.warning('当前商品已下架，不可预览')
            return
          }
          let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : [];
          let arr = [];
          if (product.albumPics) {
            arr = product.albumPics
              .split(',')
              .filter((item) => item.trim() !== '');
          } else {
            albumPicsJson = JSON.parse(albumPicsJson);
            albumPicsJson.forEach((item) => {
              if (arr.length < 10 && item.url) {
                arr.push(item.url);
              }
            });
          }
          this.productObj = item;
          this.productSn = item.productSn;
          this.gameSysinfoReadcount = item.gameSysinfoReadcount;
          this.gameSysinfoCollectcount = item.gameSysinfoCollectcount;
          this.price = item.price;
          let oldArr = this.mergeOptions(
            res.data.productAttributeList,
            res.data.productAttributeValueList
          );
          let newArr = [];
          let newArr2 = [];
          oldArr.forEach((item) => {
            if (item.selectType == 2) {
              newArr2.push(item);
            } else {
              newArr.push(item);
            }
          });
  
          newArr.sort((a, b) => {
            return b.type - a.type;
          });
          newArr2.sort((a, b) => {
            return b.sort - a.sort;
          });
          if (res.data.product.description) {
            newArr2.push({
              name: `【卖家说】${res.data.product.description}`,
              value: '',
              sort: 11,
              selectType: 2,
            });
          }
  
          let allArr = newArr.concat(newArr2);
          
          let searchArr = this.getSarchArr(res.data.productAttributeList);
          console.log(allArr,searchArr,11111)
          allArr.forEach((item) => {
            let searchName = searchArr.find((j) => {
              return j.name == item.label;
            });
            // console.log(searchName,99999)
  
            if (searchName && searchName.value && searchName.value.length > 0) {
              let nameList = searchName.value.split(',');
              nameList.sort((a, b) => b.length - a.length);
              nameList.forEach((keyword) => {
                const regex = new RegExp(`(${keyword})`, 'gi');
                if (item.selectType == 2) {
                  item.name = item.name.replace(
                    regex,
                    '<span style="color:#ff720c">$1</span>'
                  );
                }
                {
                  item.value = item.value.replace(
                    regex,
                    '<span style="color:#ff720c">$1</span>'
                  );
                }
              });
            }
          });
          // console.log(JSON.stringify(allArr),8989)
          // console.log(searchArr,7777)
          // this.tableData = newArr.concat(newArr2);
          this.tableData = allArr;
          console.log(this.tableData);
          this.playTableLoading = false;
          arr.unshift(defaultImage);
          this.arrDtPicForShow = arr;
          this.showViewer = true;
        });
        // this.arrDtPicForShow = arr
        // this.showViewer = true
      },
    },
  };
  </script>
  