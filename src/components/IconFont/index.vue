<template>
    <svg
      :style="sizeStyle"
      class="iconfont"
      aria-hidden="true"
      @click="$emit('click')"
    >
      <use :xlink:href="href"></use>
    </svg>
  </template>
  
  <script>
  import './iconfont';
  export default {
    name: 'IconFont',
    components: {},
    props: {
      icon: {
        type: String,
        default: '',
      },
      size: {
        type: Number,
        default: 14,
      },
      color: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        sizeStyle: {},
      };
    },
    computed: {
      href() {
        return '#' + 'kk-icon-' + this.icon;
      },
    },
    mounted() {
      this.sizeStyle = this.getSizeStyle();
    },
    methods: {
      getSizeStyle() {
        let styles = {
          width: this.size + 'px',
          height: this.size + 'px',
        };
        if (this.color) {
          styles.color = this.color;
        }
        return styles;
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .iconfont {
    display: inline;
    width: 1em;
    height: 1em;
    vertical-align: middle;
    fill: currentColor;
    overflow: hidden;
    margin-top: -2px;
  }
  </style>
  