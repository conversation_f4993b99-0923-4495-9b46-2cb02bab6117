<template>
  <div>
    <div class="spaceStart" style="align-items: flex-start;flex-wrap: wrap;">
      <div class="custom-image-box">
        <div
          ref="upload"
          class="spaceStart"
          style="flex-wrap: wrap;align-items: flex-start;"
        >
          <div
            v-for="(item, index) in fileList"
            :key="item.url"
            class="custom-image-item"
          >
            <el-select
              v-if="hasImgType && isImage(item.url)"
              @change="v => changeImgType(v, index)"
              v-model="item.name"
            >
              <el-option
                v-for="ele in options"
                :key="ele.name"
                :label="ele.name"
                :value="ele.name"
              ></el-option>
            </el-select>

            <el-image
              v-if="isImage(item.url)"
              class="custom-img"
              :preview-src-list="fileList.filter(f => isImage(f.url)).map(ele => ele.url)"
              :src="item.url"
              alt="uploaded image"
            />
            <div v-else class="custom-file-item">
              <i class="el-icon-document"></i>
              <span class="file-name">{{ item.name || 'Excel 文件' }}</span>
            </div>

            <el-button
              icon="el-icon-delete"
              @click="handleRemove(index)"
            ></el-button>
          </div>

          <div class="el-upload--picture-card" style="position: relative;">
            <i class="el-icon-plus" />
            <input
              type="file"
              ref="inputList"
              multiple
              @change="uploadList"
              class="picUpload_btn"
              accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp,.xls,.xlsx"
            />
          </div>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
      <el-image
        :src="dialogImageUrl"
        :preview-src-list="[dialogImageUrl]"
        width="100%"
        alt=""
      />
    </el-dialog>

    <img
      ref="waterImg"
      style="height: 0;width:0;"
      src="../../assets/images/water_pc.png"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import { policy } from '@/api/oss';
import Sortable from 'sortablejs';
import ImageCompressor from 'js-image-compressor';
import { fileByBase64, base64ToFile } from '@/utils/index';
import axios from 'axios';

const webkk = process.env.BASE_API;

export default {
  name: 'MultiUpload',
  props: {
    hasImgType: { type: Boolean, default: false },
    value: Array,
    maxCount: { type: Number, default: 100 },
    isDeletWaterList: { type: Number, default: 1 },
    options: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      dataObj: {},
      dialogVisible: false,
      dialogImageUrl: null,
      useOss: true,
      ossUploadUrl: 'https://images2.kkzhw.com',
      minioUploadUrl: `${webkk}/minio/upload`
    };
  },
  computed: {
    fileList() {
      const fileList = [];
      for (let i = 0; i < this.value.length; i++) {
        if (this.hasImgType) {
          fileList.push({ url: this.value[i].url, name: this.value[i].name });
        } else {
          fileList.push({ url: this.value[i] });
        }
      }
      return fileList;
    }
  },
  mounted() {
    this.initDragSort();
  },
  methods: {
    isImage(url) {
      return /\.(jpeg|jpg|png|gif|bmp|webp)$/i.test(url);
    },
    changeImgType() {
      this.emitInput(this.fileList);
    },
    async imgToCanvas(base64) {
      const img = document.createElement('img');
      img.setAttribute('src', base64);
      await new Promise(resolve => (img.onload = resolve));
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      canvas.getContext('2d').drawImage(img, 0, 0);
      return canvas;
    },
    async addWatermark(canvas) {
      const ctx = canvas.getContext('2d');
      const pattern = ctx.createPattern(this.$refs.waterImg, 'repeat');
      ctx.fillStyle = pattern;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      return canvas;
    },
    convasToImg(canvas) {
      const image = new Image();
      image.src = canvas.toDataURL('image/png');
      return image;
    },
    initDragSort() {
      const el = this.$refs.upload;
      Sortable.create(el, {
        onEnd: ({ oldIndex, newIndex }) => {
          const arr = this.fileList;
          const page = arr[oldIndex];
          if (!page) return;
          arr.splice(oldIndex, 1);
          arr.splice(newIndex, 0, page);
          this.emitInput(arr);
        }
      });
    },
    emitInput(fileList) {
      const value = this.hasImgType
        ? fileList.map(f => ({ url: f.url, name: f.name }))
        : fileList.map(f => f.url);
      this.$emit('input', value);
    },
    handleRemove(index) {
      this.fileList.splice(index, 1);
      this.emitInput(this.fileList);
    },
    handlePreview(file) {
      this.dialogVisible = true;
      this.dialogImageUrl = file.url;
    },
    addWater(file) {
      return new Promise((resolve) => {
        if (this.isDeletWaterList !== 1 && this.isImage(file.name)) {
          fileByBase64(file, async base64 => {
            const tempCanvas = await this.imgToCanvas(base64);
            const canvas = await this.addWatermark(tempCanvas);
            const img = this.convasToImg(canvas);
            const newFile = base64ToFile(img.src, file.name);
            resolve(newFile);
          });
        } else {
          resolve(file);
        }
      });
    },
    rename(file, fileName) {
      const copyFile = new File([file], fileName, { type: file.type });
      copyFile.uid = file.uid;
      return copyFile;
    },
    customUpload(file) {
      const formData = new FormData();
      const data = this.dataObj[file.uid];
      Object.keys(data).forEach(key => {
        if (key !== 'fileName') formData.append(key, data[key]);
      });
      formData.append('success_action_status', '200');
      formData.append('file', file, file.name);

      axios.post(this.ossUploadUrl, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      }).then(res => {
        if (res.status === 200) {
          this.handleUploadSuccess(file);
        }
      });
    },
    uploadList(event) {
      const files = event.target.files;
      for (let i = 0; i < files.length; i++) {
        let file = files[i];
        file.uid = _.uniqueId('file_');
        this.beforeUpload(file).then(data => {
          this.customUpload(data);
        });
      }
    },
    async beforeUpload(file) {
      const _self = this;
      const copyFile = await policy()
        .then(response => {
          const ext = file.name.split('.').pop();
          const uid = file.uid;
          const fileName = response.data.fileName;
          _self.dataObj[uid] = {
            fileName,
            policy: response.data.policy,
            signature: response.data.signature,
            ossaccessKeyId: response.data.accessKeyId,
            key: `${response.data.dir}/${fileName}.${ext}`,
            dir: response.data.dir,
            host: response.data.host
          };
          return this.rename(file, `${fileName}.${ext}`);
        })
        .catch(err => {
          console.log(err);
          return false;
        });

      if (!copyFile) return false;

      let newFile = await this.addWater(copyFile);
      newFile.uid = file.uid;
      return Promise.resolve(newFile);
    },
    handleUploadSuccess(file) {
      const uid = file.uid;
      const url = `${this.dataObj[uid].host}/${this.dataObj[uid].key}`;
      const obj = {
        name: file.name,
        url
      };
      if (this.hasImgType) obj.name = '';
      this.fileList.push(obj);
      this.emitInput(this.fileList);
      this.$refs.inputList.value = '';
      console.log('文件上传成功，访问路径：', url);
    },
    handleExceed(files, fileList) {
      this.$message({
        message: `最多只能上传${this.maxCount}个文件`,
        type: 'warning',
        duration: 1000
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.picUpload_btn {
  position: absolute;
  top: 0; left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.custom-image-item {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  position: relative;

  .el-select {
    width: 120px !important;
    position: absolute;
    top: 0; left: 0;
    z-index: 999;
  }
}

.custom-image-item .custom-img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  cursor: pointer;
}

.custom-image-item .custom-file-item {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #ccc;
  flex-direction: column;
  background: #f5f5f5;

  .file-name {
    margin-top: 8px;
    font-size: 14px;
    word-break: break-all;
    padding: 0 4px;
    text-align: center;
  }
}

.custom-image-item .el-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 0 0 5px 0;
  cursor: pointer;
}

/deep/ .el-upload--picture-card {
  height: 200px;
  width: 200px;
  text-align: center;
}

/deep/ .el-upload-list__item {
  display: none;
}

/deep/ .el-upload-list--picture-card .is-ready {
  display: none !important;
}
</style>
