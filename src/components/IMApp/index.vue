<template>
  <div>
    <div
      :class="$style.container"
      style="position: relative; top: 0; left: 0; transform: none"
    >
      <!-- IMUIKIT 相关内容 -->
      <div :class="$style.header">
        <div class="spaceStart" style="width: 389px">
          <div :class="$style['avatar-icon']" ref="avatar" />
          <div style="margin-left: 20px">
            {{ userInfo.account }}
          </div>
          <el-link style="margin-left: 20px" @click="showStatics" type="primary"
            >服务统计</el-link
          >
          <el-switch
            v-model="soundOpen"
            class="soundswitch"
            active-text="消息提示音"
          >
          </el-switch>
        </div>
        <div :class="$style.search" ref="search" />
        <div :class="$style.add" ref="add" />
        <el-link style="margin-left: 20px" @click="showStatics2" type="primary"
          >待处理会话</el-link
        >
        <el-dropdown @command="handelStatusChange" style="width: 100px">
          <span class="el-dropdown-link" :style="{ color: kfStatusItem.color }">
            {{ kfStatusItem.label
            }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown" class="kf-status-menu">
            <el-dropdown-item
              v-for="item in kfStatusList"
              :key="item.value"
              :command="item.value"
              :style="{ color: item.color }"
            >
              {{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <div class="logout-icon">
          <i class="iconfontnew icon-guanbi"></i>
        </div>
      </div>
      <div :class="$style.content">
        <div :class="$style.left">
          <div
            :class="{
              [$style['chat-icon']]: true,
              [$style.active]: type === 'all',
            }"
            @click="changeModel('all')"
            style="font-size: 24px !important"
          >
            <i
            style="font-size: 24px !important"
              :class="{
                [$style['iconfont']]: true,
                iconfont: true,
                'icon-message_fill_light': true,
              }"
            />
            <!-- <div :class="$style['icon-label']">全部</div> -->
          </div>
          <div
            :class="{
              [$style['chat-icon']]: true,
              [$style.active]: type === 'p2p',
            }"
            @click="changeModel('p2p')"
          >
            <i class="el-icon-user-solid" />
            <!-- <div :class="$style['icon-label']">单聊</div> -->
          </div>
          <div
            style="margin-top: -12px"
            :class="{
              [$style['chat-icon']]: true,
              [$style.active]: type === 'team',
            }"
            @click="changeModel('team')"
          >
            <i class="iconfontnew icon-kehuqunzu kehuqunzu"></i>
            <!-- <div :class="$style['icon-label']">群聊</div> -->
          </div>
          <div
            style="margin-top: -12px"
            :class="{
              [$style['chat-icon']]: true,
              [$style.active]: type === 'team-mute',
            }"
            @click="changeModel('team-mute')"
          >
            <i class="iconfontnew icon-miandarao kehuqunzu"></i>
            <!-- <div :class="$style['icon-label']">群聊</div> -->
          </div>
          <div
            style="margin-top: -12px"
            :class="{
              [$style['chat-icon']]: true,
              [$style.active]: type === 'team-history',
            }"
            @click="changeModel('team-history')"
          >
          <el-tooltip class="item" effect="dark" content="历史群聊" placement="bottom">
            <i class="iconfontnew icon-fenlei kehuqunzu"></i>
          </el-tooltip>
            
          <!-- <div
            :class="{
              [$style['contact-icon']]: true,
              [$style.active]: model === 'contact'
            }"
            @click="() => (model = 'contact')"
          >
            <i
              :class="{
                [$style['iconfont']]: true,
                iconfont: true,
                'icon-tongxunlu1': true
              }"
            />
            <div :class="$style['icon-label']">通讯录</div>
          </div> -->
        </div>
        <div
            style="margin-top: -12px"
            :class="{
              [$style['chat-icon']]: true,
              [$style.active]: type === 'team-yijia',
            }"
            @click="changeModel('team-yijia')"
          >
          <el-tooltip class="item" effect="dark" content="议价线索" placement="bottom">
            <i class="iconfontnew icon-jilu kehuqunzu"></i>
          </el-tooltip>
        </div>
        <div v-if="type === 'team-history'||type === 'team-yijia'">
          <div @scroll.stop="handleScroll" @click.stop @mousedown.stop @mouseup.stop @mousemove.stop @touchstart.stop @touchmove.stop @touchend.stop ref="scrollContainer"  style="width: 349px;height: 740px;background: #fff;position: absolute;top:0px;left: 40px;z-index: 99;overflow: auto;">
              <div style="padding: 10px;position: sticky; top: 0px;background: #fff;">
                <el-input
                  placeholder="请输入订单号、编号搜索"
                  v-model="keyword" @keyup.enter.native.stop="handleSearch">
                  <i slot="suffix" style="cursor: pointer;" @click.stop="handleSearch" class="el-input__icon el-icon-search"></i>
                </el-input>
              </div>
              <div class="teamListClass" v-for="(item,index) in teamListData" @click.stop="teamListClick(item)" :class="teamListClickId==item.id? 'teamListClassActive' : ''" >
                <div class="teamListImg" >
                  <img :src="item.imPic||'https://images2.kkzhw.com/mall/imteam/team_icon.jpg'" alt="">
                </div>
                <div class="teamListContent">
                  <div class="name" >{{item.teamName}}</div>
                  <div class="msg" ></div>
                </div>
                <div class="teamDataTime">
                  {{item.updateTime|formatCreateTime}}
                </div>
              </div>
            </div>
           <div @click.stop @mousedown.stop @mouseup.stop @mousemove.stop @touchstart.stop @touchmove.stop @touchend.stop v-if="type === 'team-history'||type === 'team-yijia'" style="position: absolute;top:0px;left: 389px;z-index: 399;">
            <div  style="width: 861px;height: 690px;background: #f6f8fa;overflow: auto;padding: 10px 16px 30px 16px;box-sizing: border-box;">
              <div v-for="(item,index) in teamMsgList">
                <div class="msgBodyTitle">
                  {{item.fromnick}}
                </div>
                <div class="msgBodyText">
                  <div v-if="item.msgtype=='TEXT'">
                    {{item.body}}
                  </div>
                  <div v-if="item.msgtype=='TIPS'">
                    {{item.body}}
                  </div>
                  <div v-if="item.msgtype=='CUSTOM'">
                    <div>
                      {{JSON.parse(item.attach).body.title}}
                    </div>
                    <div v-html="JSON.parse(item.attach).body.content"></div>
                </div>
                <div v-if="item.msgtype=='PICTURE'">
                    <img style="width: 300px;height: 150px;object-fit: contain;" :src="JSON.parse(item.attach).url" alt="">
                </div>
                </div>
                <div class="msgBodyTime">
                  {{item.msgtimestamp|formatCreateTimeDay}}
                </div>
              </div>

            </div>
              <div @click.stop @mousedown.stop @mouseup.stop @mousemove.stop @touchstart.stop @touchmove.stop @touchend.stop  v-if="type === 'team-history'" style="width: 861px;height: 50px;background: #fff;">
                <el-button v-if="teamDetail.imStatus==1||teamDetail.imStatus==2" size="medium" style="margin-left: 16px;margin-top: 8px;" @click="goMsgChat(teamDetail)" type="primary">进入会话</el-button>
                <div style="
                  font-size: 16px;
                  color: #000;
                  padding-top: 14px;
                  margin-left: 20px;" 
                  v-if="teamDetail.imStatus==3">该群已解散
                </div>
              </div>
              <div @click.stop @mousedown.stop @mouseup.stop @mousemove.stop @touchstart.stop @touchmove.stop @touchend.stop  v-if="type === 'team-yijia'" style="width: 861px;height: 50px;background: #f6f8fa;">
              </div>
           </div>
           
            <div  @click.stop @mousedown.stop @mouseup.stop @mousemove.stop @touchstart.stop @touchmove.stop @touchend.stop    class="im-box-right"  v-if="type === 'team-history'||type === 'team-yijia'" style="width: 349px;height: 800px;background: #fff;position: absolute;top:-60px;left: 1251px;overflow: auto;font-size: 14px;color: #000;z-index: 400;">
              <div v-if="teamMsgList&&teamMsgList.length">
                <div>
                  <actionCard
            :flowId="newFlowId"
            :teamInfo="newTeamInfo"
            :teamPnote="newTeamPnote"
            :teamNote="newTeamNote"
            :bidim="newBidim"
            :sidim="newSidim"
            @doSendBaopeiMsg2Team="doSendBaopeiMsg2Team"
            @doRefresh="doRefresh"
            @actionCardClickChange="actionCardClickChange1"
            class="newActionCard"
          ></actionCard>
          </div>
          <orderCard
            v-if="newHasOrderDetail"
              class="newActionCard"
            :orderDetail="newOrderDetail"
          ></orderCard>
          <div class="empty emptyone" v-else>
            <p>暂无订单信息</p>
          </div>
          <stepCard v-if="newHasFlow" :flow="newFlow"></stepCard>
          <div class="empty" v-else><p>暂无流程信息</p></div>
              </div>
            </div>
          </div>
        </div>
        <div :class="$style.right" v-show="model === 'chat'">
          <div :class="$style['right-list']" ref="conversation" />
          <!-- <div
            v-show="type === 'p2p'"
            :class="$style['right-list']"
            ref="conversation2"
          />
          <div
            v-show="type === 'team'"
            :class="$style['right-list']"
            ref="conversation3"
          />
          <div
            v-show="type === 'team-mute'"
            :class="$style['right-list']"
            ref="conversation4"
          /> -->
          <el-breadcrumb
            v-if="stepList && stepList.length"
            class="steps"
            separator-class="el-icon-arrow-right"
          >
            <el-breadcrumb-item
              v-for="(item, index) in stepList"
              :key="index"
              :class="stepActive === index ? 'stepActive' : ''"
              >{{ item.name }}</el-breadcrumb-item
            >
          </el-breadcrumb>
          <div v-if="showBar" ref="kfbar" class="kfbar">
            <div v-if="tools && tools.length" class="bar_list2">
              <div class="right_box">
                <el-button
                  type="success"
                  plain
                  :class="item.is_click == 1 ? 'clicked' : ''"
                  @click="handleTools(item)"
                  v-for="(item, index) in tools"
                  :key="index"
                >
                  {{ item.name }}
                </el-button>
              </div>
            </div>
            <div class="bar_list">
              <div class="left_box">
                <el-dropdown
                  class="dropdown_box"
                  trigger="click"
                  placement="top-start"
                  @command="inputMsg"
                >
                  <el-button type="success" plain> 常用语 </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :key="index"
                      class="oneline"
                      v-for="(item, index) in dropList"
                      :command="index"
                      divided
                      >{{ item }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
                <div>
                  <!-- <el-dropdown
                    size="mini"
                    v-if="isTeam && bidim"
                    class="dropdown_box"
                    trigger="click"
                    placement="top-start"
                    @command="bidimClick"
                  >
                    <el-button type="success" plain>
                      联系买家
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item divided command="1"
                        >呼叫买家</el-dropdown-item
                      >
                      <el-dropdown-item divided command="2"
                        >私聊买家</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-dropdown
                    size="mini"
                    v-if="isTeam && bidim"
                    class="dropdown_box"
                    trigger="click"
                    placement="top-start"
                    @command="sidimClick"
                  >
                    <el-button type="success" plain>
                      联系卖家
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item divided command="1"
                        >呼叫卖家</el-dropdown-item
                      >
                      <el-dropdown-item divided command="2"
                        >私聊卖家</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown> -->
                  <!-- <el-button
                    type="success"
                    plain
                    v-if="isTeam === false"
                    @click="callUser"
                  >
                    呼叫用户
                  </el-button>
                  <el-button type="success" plain @click="callUserPhone">
                    坐席呼叫
                  </el-button> -->
                  <el-button type="success" plain @click="showCreateTeam">
                    建群
                  </el-button>
                  <el-button type="success" plain @click="showCreateHuishou">
                    建单
                  </el-button>
                  <!-- <el-button type="success" plain @click="showCreateheTong">
                    创建合同
                  </el-button> -->
                  <el-dropdown
                  class="dropdown_box"
                  trigger="click"
                  placement="top-start"
                  @command="showCreateheTong"
                >
                  <el-button type="success" plain> 创建合同 </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="BILATERAL" divided>两方合同</el-dropdown-item>
                    <el-dropdown-item command="TRIPARTITE" divided>三方合同</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                  <!-- <el-button type="success" plain @click="resetPassword">
                    重置用户密码
                  </el-button> -->
                  <!-- <el-button type="success" plain @click="goChatByUserName">
                    私聊
                  </el-button> -->
                </div>
              </div>
              <div class="right_box">
                <el-button
                  v-if="!isTeam"
                  type="success"
                  plain
                  @click="beSimilarGoods"
                >
                  相似商品
                </el-button>
                <el-button
                  v-if="!isTeam"
                  type="success"
                  plain
                  @click="btnClick"
                >
                  发起录号
                </el-button>
                <el-button
                  type="success"
                  plain
                  v-if="isTeam"
                  @click="toggleMute"
                >
                  {{ muteTxt }}
                </el-button>
                <el-button
                  type="success"
                  plain
                  v-if="isTeam"
                  @click="addPerson"
                >
                  拉人
                </el-button>
                <el-button
                  type="success"
                  plain
                  v-if="!isTeam"
                  @click="showYijia"
                >
                  发送议价
                </el-button>
                <div style="line-height: 1">
                  <el-dropdown
                    size="mini"
                    v-if="!isTeam"
                    class="dropdown_box"
                    trigger="click"
                    placement="top-start"
                    @command="callClick"
                  >
                    <el-button type="success" plain> 呼叫 </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item divided command="3"
                        >微信提醒</el-dropdown-item
                      >
                      <el-dropdown-item divided command="1"
                        >人工呼叫</el-dropdown-item
                      >
                      <el-dropdown-item divided command="2"
                        >智能呼叫</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <el-button
                  type="success"
                  plain
                  v-if="isTeam === false"
                  @click="showRecordByKF"
                >
                  咨询小记
                </el-button>
                <el-button
                  type="success"
                  plain
                  v-if="isTeam === false"
                  @click="doShowRecordP2p"
                >
                  备注用户
                </el-button>
                <el-button
                  type="success"
                  plain
                  v-if="isTeam"
                  @click="doShowRecordTeam"
                >
                  备注群
                </el-button>
                <!-- <el-button type="success" plain @click="showOrderDialog">
                  订单工具
                </el-button> -->
                <!-- <el-button type="success" plain @click="showCreateDanbaoByKF()">
                  担保建群
                </el-button> -->
                <el-button
                  v-if="isTeam"
                  type="success"
                  plain
                  @click="showCreateTimeout"
                >
                  计时提醒
                </el-button>
                <el-button
                  v-if="isTeam"
                  type="success"
                  plain
                  @click="showChangeIMTeamName"
                >
                  更改群名
                </el-button>
                <el-button type="success" plain @click="onFindUser">
                  找人
                </el-button>

                <el-button type="success" plain @click="onSendKF">
                  发送客服
                </el-button>

                <!-- <el-button type="success" plain @click="showCreateHuishou()">
                  快捷建单
                </el-button> -->

                <!-- <el-button
                  v-if="isTeam === true"
                  type="success"
                  plain
                  @click="showBukuan"
                >
                  补款链接
                </el-button> -->
              </div>
            </div>
          </div>
          <div :class="$style['right-content']" ref="chat" />
        </div>
        <div :class="$style.right" v-show="model === 'contact'">
          <div :class="$style['right-list']" ref="contactList" />
          <div :class="$style['right-content']" ref="contactInfo" />
        </div>
      </div>
    </div>
    <el-drawer
      title=""
      :visible.sync="showStatics2Layer"
      :direction="drawerRlt"
      :modal="false"
      class="myDrawer"
      style="width: 573px"
      :wrapperClosable="false"
      :modal-append-to-body="false"
      :before-close="() => (showStatics2Layer = false)"
    >
      <!-- v-show="showStatics2Layer" -->
      <div class="statics2Layer">
        <div class="spaceBetween">
          <el-link
            style="position: absolute; top: 25px; right: 100px"
            @click="onRefresh"
            type="primary"
            >刷新</el-link
          >
          <!-- <i
            @click="showStatics2Layer = false"
            class="el-icon-close"
            style="font-size: 30px; cursor: pointer"
          ></i> -->
        </div>
        <el-tabs v-model="staticsType" stretch @tab-click="changeStaticsType">
          <el-tab-pane label="待处理会话" name="wait"></el-tab-pane>
          <el-tab-pane label="最近已完成会话" name="finish"></el-tab-pane>
        </el-tabs>

        <el-table
          :data="todoListFed.length ? todoListFed : todoList"
          class="todoList"
          border
        >
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getType(scope.row.teamState)">
                {{ getTeamState(scope.row.teamState) }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column width="140" label="用户IM" align="center">
            <template slot-scope="scope">
              <el-link
                @click="handleBuyerIm(scope.row.buyerim, scope.row)"
                type="primary"
              >
                {{ scope.row.buyerim }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="更新时间" width="160" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.updateTime">{{
                util.timeFormat(scope.row.updateTime)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column width="160" align="center">
            <template slot="header" slot-scope="scope">
              <el-input
                v-model="searchFed"
                size="mini"
                placeholder="输入关键字搜索"
                clearable
                @keyup.enter.native="doSearchFed"
                @clear="clearSearchFed"

              />
            </template>
            <template slot-scope="scope">
              <div class="spaceStart">
                <div class="note">
                  <el-popover
                    placement="top-start"
                    title="备注"
                    width="200"
                    trigger="click"
                    :content="scope.row.teamPnote"
                  >
                    <div class="note" slot="reference">
                      {{ scope.row.teamPnote }}
                    </div>
                  </el-popover>
                </div>
                <div v-if="staticsType == 'wait'">
                  <i @click="doEdit(scope.row)" class="el-icon-edit"></i>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    <!-- <div v-show="showRightBox" class="right_box_content"> -->
    <!-- <div v-show="showRightBox" class="right_box_content"> -->
    <el-drawer
      title="发起录号"
      :visible.sync="showRightBox"
      :direction="drawerRlt"
      :modal="false"
      class="myDrawer"
      :wrapperClosable="false"
      :modal-append-to-body="false"
      :before-close="() => (showRightBox = false)"
    >
      <el-form
        :model="ruleForm"
        ref="ruleForm"
        label-width="100px"
        style="margin-top: 20px; padding-right: 20px"
        :disabled="stateImg ? true : false"
      >
        <el-form-item label="用户账号">
          <el-input v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="游戏账号">
          <el-input v-model="ruleForm.account"></el-input>
        </el-form-item>
        <el-form-item label="商品编号">
          <el-input v-model="ruleForm.productSn"></el-input>
          <div style="color: red; font-size: 12px">
            如若不填讲生成新的商品编号
          </div>
        </el-form-item>
        <el-form-item style="margin-top: -27px" label="价格">
          <el-input v-model="ruleForm.price"></el-input>
        </el-form-item>
        <el-form-item label="区服">
          <el-cascader
            style="width: 100%"
            v-model="ruleForm.gameAccountQufu"
            :options="optionsList"
          ></el-cascader>
        </el-form-item>
        <el-form-item v-if="!stateImg">
          <el-button @click="showRightBox = false">取消</el-button>
          <el-button type="primary" @click="rightBoxSubmit('ruleForm')"
            >确定</el-button
          >
        </el-form-item>
      </el-form>
      <div v-show="loadingFlag" class="right_box_content_top_box">
        <div class="right_box_content_top_box_content">
          <div>{{ luhaoText }}</div>
          <i class="el-icon-loading" style="font-size: 30px"></i>
        </div>
      </div>
      <div
        v-if="!luhaosuccess && stateImg"
        style="text-align: center; margin-top: -10px; font-size: 16px"
      >
        请截图以下二维码发送给用户
      </div>
      <div
        v-if="!luhaosuccess && stateImg"
        style="
          margin-top: -10px;
          font-size: 16px;
          margin-top: 10px;
          margin-bottom: 10px;
          color: #67c23a;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        等待用户扫码<i class="el-icon-loading" style="font-size: 30px"></i>
      </div>
      <p
        v-if="luhaosuccess"
        style="
          text-align: center;
          margin-top: 20px;
          color: #67c23a;
          font-size: 20px;
        "
      >
        用户已完成登录
      </p>
      <img
        style="margin-left: 50%; transform: translate(-50%)"
        v-if="stateImg"
        :src="stateImg"
      />
    </el-drawer>
    <!-- </div> -->
    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="showRecordByKFDialog"
      append-to-body
    >
      <el-form
        :model="postFormRecord"
        ref="recordForm"
        :rules="recordRules"
        label-width="100px"
      >
        <el-form-item style="display: none" label="咨询内容">
          <el-select style="width: 100%" v-model="postFormRecord.consultType">
            <el-option
              v-for="item in recordList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="productSn" label="商品编号">
          <el-input v-model="postFormRecord.productSn"></el-input>
        </el-form-item>
        <el-form-item prop="consultContent" label="小记内容">
          <el-input
            type="textarea"
            v-model="postFormRecord.consultContent"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="showRecordByKFDialog = false">取 消</el-button>
      <el-button type="primary" @click="doRecordByKFDialog">确 定</el-button>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="showRecordP2p"
      append-to-body
    >
      <el-form label-width="100px">
        <el-form-item label="手机号">
          <el-input v-model="recordP2pForm.userName"></el-input>
        </el-form-item>
        <el-form-item label="备注内容">
          <el-input
            type="textarea"
            v-model="recordP2pForm.consultContent"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="showRecordP2p = false">取 消</el-button>
      <el-button type="primary" @click="doRecordP2p">确 定</el-button>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="showRecordTeam"
      append-to-body
    >
      <el-form
        ref="recordTeamForm"
        :model="recordTeamForm"
        :rules="recordRules2"
        label-width="100px"
      >
        <el-form-item label="备注内容" prop="consultContent">
          <el-input
            type="textarea"
            v-model="recordTeamForm.consultContent"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="showRecordTeam = false">取 消</el-button>
      <el-button type="primary" @click="doRecordTeam">确 定</el-button>
    </el-dialog>

    <!-- <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="dialogVisible"
      append-to-body
    >
      <el-form label-width="100px">
        <el-form-item label="游戏">
          <el-select
            :filterable="true"
            style="width:100%"
            v-model="postForm.cateId"
          >
            <el-option
              v-for="item in gameList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="买家账号">
          <el-input v-model="postForm.buyerUsername"></el-input>
        </el-form-item>
        <el-form-item label="卖家账号">
          <el-input v-model="postForm.sellerUsername"></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="doCreateDanbaoByKF">确 定</el-button>
    </el-dialog> -->

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="dialogVisible3"
      append-to-body
    >
      <el-form label-width="100px">
        <el-form-item label="游戏">
          <el-select
            :filterable="true"
            style="width: 100%"
            v-model="postForm3.cateId"
          >
            <el-option
              v-for="item in gameList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select
            :filterable="true"
            style="width: 100%"
            v-model="postForm3.teamType"
          >
            <el-option
              v-for="item in teamType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="postForm3.teamType == '0'" label="订单编号">
          <el-input v-model="postForm3.orderSn"></el-input>
        </el-form-item>
        <el-form-item label="买家账号">
          <el-input v-model="postForm3.buyerUsername"></el-input>
        </el-form-item>
        <el-form-item label="卖家账号">
          <el-input v-model="postForm3.sellerUsername"></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="dialogVisible3 = false">取 消</el-button>
      <el-button type="primary" @click="doCreateTeamByKF">确 定</el-button>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="orderDialog"
      append-to-body
    >
      <el-form label-width="100px">
        <el-form-item label="订单编号">
          <el-input v-model="postFormOrder.orderId"></el-input>
        </el-form-item>
        <el-form-item label="是否覆盖">
          <el-radio-group v-model="postFormOrder.isCover">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="orderUrl">{{ orderUrl }}</div>
      <el-button @click="orderDialog = false">取 消</el-button>
      <el-button type="primary" @click="searchOrder">查 询</el-button>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="createTimeoutDialog"
      append-to-body
    >
      <el-form label-width="100px">
        <el-form-item label="到期时间">
          <el-date-picker
            v-model="timeoutEnd"
            type="datetime"
            placeholder="选择日期时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="提醒文案">
          <el-select style="width: 220px" v-model="timeoutWaringTxt">
            <el-option
              v-for="item in waringList"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-button @click="createTimeoutDialog = false">取 消</el-button>
      <el-button type="primary" @click="timeoutSubmit">确 定</el-button>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="bukuanDialog"
      append-to-body
    >
      <el-form :model="bukuanForm" label-width="100px">
        <el-form-item label="游戏">
          <el-select style="width: 100%" v-model="bukuanForm.cateId">
            <el-option
              v-for="item in gameList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="买家手机号">
          <el-input v-model="bukuanForm.buyerUsername"></el-input>
        </el-form-item>
        <el-form-item label="卖家手机号">
          <el-input v-model="bukuanForm.sellerUsername"></el-input>
        </el-form-item>
        <el-form-item
          :rules="[
            { required: true, message: '请输入价格', trigger: 'blue' },
            { pattern: /^\d+$/, message: '请输入整数', trigger: 'blur' },
          ]"
          label="价格"
          prop="price"
        >
          <el-input v-model="bukuanForm.price"></el-input>
        </el-form-item>
      </el-form>
      <div class="orderUrl" v-if="bukuanUrl">订单Sn：{{ bukuanUrl }}</div>
      <el-button @click="bukuanDialog = false">取 消</el-button>
      <el-button type="primary" @click="createBukuan">生成补款订单</el-button>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="forward2FormDialog"
      append-to-body
    >
      <el-form ref="forward2Form" :model="forward2Form" label-width="100px">
        <el-form-item
          prop="productSn"
          :rules="[
            { required: true, message: '请输入商品编号', trigger: 'blur' },
          ]"
          label="商品编号:"
        >
          <el-input v-model="forward2Form.productSn"></el-input>
        </el-form-item>
        <el-form-item label="买家问题">
          <el-input v-model="forward2Form.question"></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="forward2FormDialog = false">取 消</el-button>
      <el-button type="primary" @click="forward2">确 定</el-button>
    </el-dialog>

    <el-dialog width="30%" title="呼叫用户" :visible.sync="callDialog">
      <el-form :model="callDialogForm">
        <el-form-item label="呼叫类型">
          <el-select
            v-model="callDialogForm.callType"
            placeholder="请选择呼叫类型"
          >
            <el-option label="用户咨询呼叫" value="1833802"></el-option>
            <el-option label="买家下单呼叫" value="1423701"></el-option>
            <el-option label="审核呼叫" value="1484700"></el-option>
            <el-option label="呼叫买家，卖家有答复" value="2299446"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="callDialog = false">取 消</el-button>
        <el-button type="primary" @click="doCall">确 定</el-button>
      </div>
    </el-dialog>

    <el-image-viewer
      :on-close="onCloseView"
      v-if="showViewer"
      :url-list="urlList"
    />

    <el-dialog
      width="30%"
      title="单聊用户"
      :visible.sync="showGoChatByUserName"
    >
      <el-form ref="goChatByUserNameForm" :model="goChatByUserNameForm">
        <el-form-item
          label="手机号或商品编号"
          prop="username"
          :rules="[
            {
              required: true,
              message: '请输入手机号或商品编号',
              trigger: 'blur',
            },
          ]"
        >
          <el-input v-model="goChatByUserNameForm.username"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showGoChatByUserName = false">取 消</el-button>
        <el-button type="primary" @click="goChatByUserNameReal"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <kferTransfer
      v-if="showKferTransfer"
      :dialogVisible="showKferTransfer"
      :actionObj="actionObj"
      :actionData="actionData"
      :orderDetail="orderDetail"
      @close="hideKferTransfer"
    >
    </kferTransfer>

    <hetong
      v-if="showHetong"
      :dialogVisible="showHetong"
      :actionObj="actionObj"
      :actionData="actionData"
      :orderDetail="orderDetail"
      :hetongTypeProp="hetongType"
      @close="showHetong = false"
    >
    </hetong>

    <!-- 回收订单和担保订单 -->
    <dingdan0
      @hide="hideDingdan2"
      v-if="dialogVisible2"
      :dialogVisible="dialogVisible2"
    >
    </dingdan0>

    <dingdan1
      @hide="hideDingdan1"
      v-if="dialogVisible1"
      :dialogVisible="dialogVisible1"
    >
    </dingdan1>

    <yijia
      @hide="showYijiaModal = false"
      v-if="showYijiaModal"
      :dialogVisible="showYijiaModal"
    >
    </yijia>

    <el-dialog
      width="800px"
      title="客服列表"
      class="kflist"
      :visible.sync="showKfList"
    >
      <kflist ref="kfListRef" :teamId="teamId" @close="closeKfList"></kflist>
    </el-dialog>

    <changeIMTeamName
      v-if="showChangeIMTeamNameModal"
      :dialogVisible="showChangeIMTeamNameModal"
      :teamId="teamId"
      @close="showChangeIMTeamNameModal = false"
    >
    </changeIMTeamName>

    <findUser
      @hide="showFindUser = false"
      v-if="showFindUser"
      :dialogVisible="showFindUser"
    >
    </findUser>

    <sendKf
      @hide="showSendKF = false"
      v-if="showSendKF"
      :dialogVisible="showSendKF"
    >
    </sendKf>

    <staticsDialog
      v-if="showStaticsDialog"
      @hide="showStaticsDialog = false"
      :dialogVisible="showStaticsDialog"
    >
    </staticsDialog>

    <div>
      <audio ref="audio" src="../../../static/msg.mp3"></audio>
    </div>

    <editDialog
      v-if="showEditDialog"
      @hide="showEditDialog = false"
      :dialogVisible="showEditDialog"
      :teamPnote="teamPnote"
      :imteamId="imteamId"
      @refreshList="refreshList"
    >
    </editDialog>
    <el-dialog
      :visible.sync="passwordVisible"
      title="重置用户密码"
      width="500px"
      top="1vh"
    >
      <el-form ref="adminForm" :model="adminForm" label-width="150px" size="small">
        <el-form-item label="用户账号：">
          <el-input
          v-model="adminForm.userName"
            style="width: 250px"
           
          />
        </el-form-item>
        <el-form-item label="密码：">
          <el-input
          v-model="adminForm.pwd0"
            style="width: 250px"
          
          />
        </el-form-item>
        <el-form-item label="确认密码：">
          <el-input
            v-model="adminForm.pwd1"
            style="width: 250px"
           
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="passwordVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="handleDialogConfirmPwd()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="selectGameFlag"
      title="发起录号"
      width="500px"
      top="1vh"
    >
      <el-form ref="adminForm"  label-width="150px" size="small">
        <el-form-item label="选择游戏">
          <el-select  style="width: 100%" v-model="gameValue">
            <el-option
              v-for="item in luhaoGameList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="selectGameFlag = false"
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="luhaoSubmit()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    
    <el-dialog
      :visible.sync="dialogGoodsVisible"
      title="相似商品"
      width="1100px"
      top="1vh"
      center
    >
      <div style="margin-bottom: 10px;">
        商品编号
        <el-input
            v-model="wzrySameProductCode"
            style="width: 200px"
             size="small"
            placeholder="请输入商品编号"
           />
           <el-button @click="goSameProduct" style="margin: 0px 20px;" type="primary" size="medium">找号</el-button>
           <span style="color: red;">
            提示：目前只支持王者荣耀
           </span>
      </div>
      <div style="display:flex;align-items: center;">
        其他条件
        <el-input
            v-model="keyword2"
            placeholder="请输入内容"
            prefix-icon="el-icon-search"
            style="width: 200px;margin-left:4px"
            clearable
            @input="handelOptsSearch"
             size="small"
          />
          <InputNumber
            style="margin-left: 10px;"
            v-for="(item, index) in inputAttributeList"
            :item="item"
            :key="index"
            @change="(v, i) => handelInputAttrChange(index, v, i)"
          />
      </div>
      <div>
          <div
            v-if="keyword2 && (!optsSearchResult || !optsSearchResult.length)"
            style="
              color: rgba(0, 0, 0, 0.4);
              font-family: PingFang SC;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              letter-spacing: 0.56px;
              margin-top: 10px;
            "
          >
            暂无符合条件的筛选项
          </div>
          <div v-if="optsSearchResult&&optsSearchResult.length" style="display: flex; align-items: center; flex-wrap: wrap;height: 200px;overflow: auto;margin-top: 10px;">
            <div
              v-for="(item,index) in optsSearchResult"
              :key="index"
              :class="getOptIsCheck(item) ? 'active' : ''"
              class="opt-item spaceAround"
              @click="handelOptClick(item)"
            >
              {{ item.value }}
            </div>
          </div>
      </div>
      <div style="display: flex;margin-top: 10px;" v-if="selectValueList().length">
        <div style="width: 60px;margin-top: 6px;">
          您已选择
        </div>
        <div class="spaceStart flexWrap" style="flex: 1">
            <span
              v-for="item in selectValueList()"
              :key="item.value"
              class="opt-item"
              style="margin-right: 12px;"
              @click="handelOptClick(item)"
            >
              {{ item.value }}&nbsp;<i
                class="el-icon-close"
                style="font-size: 14px; cursor: pointer"
              ></i>
            </span>
          </div>
      </div>
      <div
            class="spaceStart sortItemBox"
            style="flex-wrap: wrap; margin-top: 10px"
          >
            <div
              v-for="(item, index) in comprehensiveData"
              :class="item.sort != '' ? 'active' : ''"
              :key="item.sortId"
              class="spaceStart sort_item"
              @click="sortChos(item)"
            >
              <div>{{ item.sortName }}</div>
              <IconFont
                v-if="item.sort == '' && item.value != ''"
                :size="15"
                style="margin: 0 0 0 4px"
                icon="sort"
              />

              <IconFont
                v-if="item.sort == 'asc' && item.value != ''"
                :size="15"
                style="margin: 0 0 0 4px"
                icon="asc"
              />
              <IconFont
                v-if="item.sort == 'desc' && item.value != ''"
                :size="15"
                style="margin: 0 0 0 4px"
                icon="desc"
              />
            </div>
          </div>
      <div>
            <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            prop="productSn"
            label="商品编号"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            prop="pic"
            label="商品主图"
            width="100"
            align="center">
            <template slot-scope="scope">
              <div class="goodsItem_pic_img">
                <img :src="scope.row.pic" alt="商品主图" style="width: 80px; height: 80px;border-radius: 12px;" v-if="scope.row.pic"/>
              <div @click.stop="showImagePriview(scope.row)"
                          class="goodsItem_pic_img_box">预览</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="price"
            label="商品价格"
            width="100"
            align="center">
            <template slot-scope="scope">
              ¥{{ scope.row.price||0 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="publishDay"
            label="上架时间"
            width="120"
            align="center">
          </el-table-column>
          <el-table-column
            prop="商品标题"
            label="商品标题"
            align="center"
          >
          <template slot-scope="scope">
            <el-tooltip
              class="tooltipItemBox"
              effect="light"
              :content="getWzText(scope.row.attrValueList,scope.row.name,scope.row)"
              placement="top"
              popper-class="custom-tooltip-width"
            >
              <div class="text_linThree custom-tooltip-text-label" style="text-align: center;">{{getWzText(scope.row.attrValueList,scope.row.name,scope.row)}}</div>
            </el-tooltip>
          </template>
          </el-table-column>
          <!-- <el-table-column
            prop="sscore"
            label="命中得分"
            width="100"
            align="center">
            <template slot-scope="scope">
              <span>
                {{ scope.row.sscore !== undefined && scope.row.sscore !== null ? Number(scope.row.sscore).toFixed(10) : '-' }}
              </span>
            </template>
          </el-table-column> -->
          <el-table-column
            prop="具体相似度"
            label="具体相似度"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            label="操作"
            width="100"
            align="center">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="sendGoodsCard(scope.row)">发送</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="text-align: center;" slot="footer">
        <el-button type="primary" @click="dialogGoodsVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    <swperImagePriviewPage ref="swperImagePriviewPage"/>
  </div>
</template>
<script>
import _ from 'lodash';
import editDialog from './editDialog.vue';
import sendKf from './sendKf.vue';
import findUser from './findUser.vue';
import changeIMTeamName from './changeIMTeamName.vue';
import kflist from './kflist.vue';
import indexedDBMixin from './indexedDBMixin.js';
import { autorun } from 'mobx';
import kferTransfer from './kferTransfer.vue';
import hetong from './hetong.vue';
import dingdan0 from './dingdan0.vue';
import dingdan1 from './dingdan1.vue';
import yijia from './yijia.vue';
import staticsDialog from './staticsDialog.vue';
import swperImagePriviewPage from '@/components/imagePriview/page.vue'
import { Loading } from 'element-ui';
import {getProductAttributeAttrInfo} from '@/api/bmc'
import {
  memberManageCreate2,
  memberManageUpdate2,
  getMemberDetail,
  memberManageList,
  updatePassword2,
  memberManageUpdateSimple,
  memberManageDelete
} from '@/api/member';
import {
  ComplexAvatarContainer,
  ConversationContainer, // 会话列表组件
  ChatContainer, // 聊天（会话消息）组件
  AddContainer, // 搜索——添加按钮组件
  SearchContainer, // 搜索——搜索组件
  ContactListContainer, // 通讯录——通讯录导航组件
  ContactInfoContainer, // 通讯录——通讯录详情组件，包含好友列表、群组列表以及黑名单列表
  MyAvatarContainer, // 用户资料组件
} from '@/components/im-kit-ui';
import '@/components/im-kit-ui/es/style/css';
import { formatDate } from '@/utils/date';
import myStore from '@/components/im-kit-ui/es/conversation/myStore';
import { compile } from 'jsx-web-compiler';
import './iconfont.css';
import '../../assets/newicon/iconfont.css';
import InputNumber from './ipuntNumber.vue';
import {
  getIminfo,
  consultRecordsCreate,
  createSellerFormURL,
  actionTimeClockSet,
  generateKKOrderByKF,
  soundCall,
  kferCallBar,
  wxMsgCall,
  loadByIM,
  createTeamByKF,
  flowStepDetail,
  getImaccount2,
  setTeamQuiet,
  personChatClick,
  contractDetail,
  detailBySn,
  changeStatus,
  getUserInfo,
  productAttributeListall,
  taskCreate,
  recordTaskDetail,
  getHisTeamList,
  getHisNegoTeamList,
  chatMessages,
  inTeamAndActive
} from '@/api/kf.js';
import request from '@/utils/request';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import { fetchList } from '@/api/productCate';
import actionCard from '@/components/IMCard/actionCard.vue';
import stepCard from '@/components/IMCard/stepCard.vue';
import orderCard from '@/components/IMCard/orderCard.vue'
import { personChatTodoList, personChatDoneList } from '@/api/imteamManage';
import { getOrderDetail, getFlowState ,getWzrySameProductList} from '@/api/order';
import util from '@/utils/index';
import { GOOD_TITLE, GOOD_TITLE_OTHER } from '@/utils/const.js';
// import { locale } from 'moment';
import moment from 'moment-timezone';
const dropList = [
  '卖家老板有换绑CD吗',
  '两位老板看一下卡片内容，根据小助理的提示来进行对应的操作。如有需要客服协助请艾特我',
  '卖家老板截图网易支付未实名卡片',
  '老板你好，账号经客服查验为黑号，之前可能存在过找回、跑单、纠纷等问题，平台是不能交易黑号的，这边帮你安排原路全额退款取消交易了。如果后续卖家被拉黑的原因查证并解决，再咨询买家老板有没有重新交易的意愿，目前卖家的账号已经下架全网拉黑处理！',
  '两位老板请仔细确认易须知，如确认无误请回复【同意】',
  '请买家老板根据提供信息前往验证账号，如确认账号无误请点击上方验号完成@买家老板',
  '验号无误请买家老板截图角色面板，发送UID账号',
  '卖家老板请根据指引前往换绑账号，完成换绑后请点击上方【上传截图】',
  '买家老板请根据上述指引前往修改账号信息，完成后请点击上方【确认换绑完成】',
  '合同己发送至卖家老板手机短信，请点击短信链接签署',
];
const clazzmap = {
  '建群': '#60cfa7',
  '换绑': '#e9749d',
  '验号': '#f9b751',
};

const kfStatusList = [
  {
    value: 1,
    label: '在线',
    color: 'green',
  },
  {
    value: 2,
    label: '挂起',
    color: 'red',
  },
];

export default {
  name: 'IMApp',
  mixins: [indexedDBMixin],
  components: {
    ElImageViewer,
    kferTransfer,
    dingdan0,
    dingdan1,
    kflist,
    changeIMTeamName,
    yijia,
    findUser,
    sendKf,
    staticsDialog,
    editDialog,
    hetong,
    actionCard,
    stepCard,
    orderCard,
    swperImagePriviewPage,
    InputNumber
  },
  filters: {
    formatCreateTime(time) {
      const date = new Date(time);
      return formatDate(date, 'YYYY-MM-DD');
    },
    formatCreateTimeDay(time) {
      const date = new Date(time);
      return formatDate(date, 'YYYY-MM-DD hh:mm:ss');
    },
  },
  props: {
    tools: {
      type: Array,
      default: () => {
        return [];
      },
    },
    sidim: {
      type: String,
      default: '',
    },
    bidim: {
      type: String,
      default: '',
    },
    orderDetail: {
      type: Object,
      default() {
        return {};
      },
    },
    flowId: {
      type: [String, Number],
      default: '',
    },
  },
  data: function () {
    return {
      comprehensiveData:[
        {
          sortName: '上架时间',
        sort: '',
        order: 'publishTime',
        sortId: 0,
        },
        {
          sortName: '价格',
        sort: '',
        order: 'price',
        sortId: 1,
        }
      ],
      wzrySameProductCode:'',
      tableData:[],
      tableDataOrigin2:[],
      checkBoxAttributeList: [],
      axiosType:'',
      flagPage:true,
      newHasFlow: false,
      newHasOrderDetail: false,
      newOrderDetail: {},
      newFlow: '',
      newFlowId:'',
      newTeamInfo:{},
      newTeamPnote:'',
      newTeamNote:'',
      newBidim:'',
      newSidim:'',
      teamIdChat:'',
      keyword:'',
      teamListClickId:'',
      teamListData:[],
      gameValue:'',
      luhaoGameList:[
        {
          name: "逆水寒手游",
          attriCateId:17,
          id:75
        },
        {
          name: "王者荣耀",
          attriCateId:13,
          id:82
        },
      ],
      selectGameFlag:false,
      hetongType:'',
      adminForm:{
        pwd0:'',
        pwd1:'',
        userName:''
      },
      passwordVisible:false,
      drawerRlt: 'rtl',
      luhaoText: '正在上号中,请勿关闭页面',
      kfStatusList,
      kfStatus: 1,
      util,
      staticsType: 'wait',
      showHetong: false,
      soundOpen: true,
      showFindUser: false,
      muteTxt: '开启免打扰',
      showChangeIMTeamNameModal: false,
      recordRules: {
        productSn: [{ required: true, message: '请输入编号', trigger: 'blue' }],
        consultContent: [
          { required: true, message: '请输入内容', trigger: 'blue' },
        ],
      },
      recordRules2: {
        consultContent: [
          { required: true, message: '请输入内容', trigger: 'blue' },
        ],
      },
      teamType: [
        {
          label: '普通群',
          value: '-1',
        },
        {
          label: '回收群',
          value: '1',
        },
        {
          label: '担保群',
          value: '2',
        },
        // {
        //   label: '订单群',
        //   value: '0'
        // }
      ],
      teamId: '',
      showKfList: false,
      showRecordP2p: false,
      recordP2pForm: {
        consultContent: '',
        consultTo: '',
        consultType: 0,
        userName: '',
      },
      showRecordTeam: false,
      recordTeamForm: {
        consultContent: '',
        consultTo: '',
        consultType: 6,
        flowTeamId: '',
      },
      goChatByUserNameForm: {
        username: '',
      },
      showGoChatByUserName: false,
      dialogVisible1: false,
      callDialog: false,
      callDialogForm: {
        callType: '',
        memberIM: '',
      },
      forward2FormDialog: false,
      forward2Form: {
        productSn: '',
        question: '',
      },
      createBukuanLoading: false,
      bukuanUrl: '',
      bukuanForm: {
        sellerUsername: '',
        buyerUsername: '',
        price: '',
        cateId: '',
      },
      bukuanDialog: false,
      timeoutWaringTxt: '',
      waringList: [
        '换绑CD时间到，请卖家老板确认是否收到短信',
        '老板可以继续流程吗',
      ],
      timeoutEnd: '',
      createTimeoutDialog: false,
      urlList: [],
      showViewer: false,
      userInfo: {
        nick: '',
        account: '',
      },
      type: 'all',
      orderUrl: '',
      iscover: 0,
      model: 'chat',
      dropList,
      isTeam: null,
      orderDialog: false,
      dialogVisible: false,
      dialogVisible2: false,
      dialogGoodsVisible:false,
      postFormOrder: {
        orderId: '',
        isCover: 1,
      },
      showRecordByKFDialog: false,
      postForm: {
        cateId: '',
        sellerUsername: '',
        buyerUsername: '',
        kfIM: '',
      },
      dialogVisible3: false,
      postForm3: {
        orderSn: undefined,
        teamType: '',
        cateId: '',
        sellerUsername: '',
        buyerUsername: '',
        kfIM: '',
      },
      postFormRecord: {},
      luhaosuccess: false,
      gameList: [],
      // recallList: [],
      recordList: [
        {
          id: 0,
          name: '买家咨询',
        },
        {
          id: 1,
          name: '卖家咨询',
        },
      ],
      stepList: [],
      orderBaopeiList1: [],
      actionObj: {},
      actionData: {},
      showKferTransfer: false,
      showYijiaModal: false,
      showSendKF: false,
      showStaticsDialog: false,
      showStatics2Layer: false,
      todoList: [],
      originalTodoList: [],
      showEditDialog: false,
      searchFed: '',
      todoListFed: [],
      showRightBox: false,
      ruleForm: {
        username: '',
        productSn: '',
        account: '',
        price: '',
        gameAccountQufu: [],
      },
      stateImg: '',
      loadingFlag: false,
      optionsList: [],
      isLoading:false,
      pageSize:30,
      pageNum:1,
      teamTotal:0,
      teamMsgList:[],
      teamDetail:{},
      optsSearchResult:[],
      keyword2:'',
      orderSortName:'',
      orderSort:'',
      inputAttributeList:[
      {
          'name': '价格区间',
          'selectType': 0,
          'inputType': 0,
          'inputList': '',
          'sort': 0,
          'filterType': 0,
          'searchType': 2,
          'type': 1,
          "key":"price",
          'searchSort': 0,
          selectValue: [],
          defaultValue: [],
        },
      ],
    };
  },
  computed: {
    showBar() {
      return (
        this.isTeam !== null &&
        this.$store.getters.roles.some((role) => role.includes('客服'))
      );
    },
    orderNeedSn() {
      return this.postForm2.orderType == 8;
    },
    kfStatusItem() {
      return (
        this.kfStatusList.filter((item) => item.value === this.kfStatus)[0] ||
        {}
      );
    },
  },
  created() {
    // document.addEventListener('paste', this.onPaste);
    this.throttledGetDetailForCard = _.throttle((sessionId) => {
      const teamId = sessionId.replace('team-', '');
      this.getDetailForCard(teamId);
    }, 2000);

    getUserInfo().then((res) => {
      if (res.code == 200) {
        const { onlineStatus } = res.data;
        this.kfStatus = onlineStatus;
      }
    });
  },
  beforeDestroy() {
    if (this.disposeAutorun) {
      this.disposeAutorun();
    }
    // document.removeEventListener('paste', this.onPaste);
  },
  mounted() {
    this.customRender();
    const imbox = document.getElementById('im-box');
    imbox.addEventListener('click', this.previewImg, false);
    this.openDB();
  },
  methods: {
    handelInputAttrChange(index, v) {
      const list = this.checkBoxAttributeList.concat(this.inputAttributeList);
      let flag = false;
      let hasValueName = '';
      // 找出当前已有值的属性名
      list.forEach((item) => {
        if (
          item.selectValue &&
          item.selectValue.length &&
          item.selectValue.some((val) => val !== '')
        ) {
          flag = true;
          hasValueName = item.name;
        }
      });
      // 如果已有值且不是当前要修改的属性,则不允许修改
      if (
        this.isUniqueFlag == 0 &&
        flag &&
        hasValueName !== this.inputAttributeList[index].name
      ) {
        this.$message.error('不是唯一只能选择一个属性');
        return;
      }
      this.$set(this.inputAttributeList[index], 'selectValue', v);
    },
    sortChos(date) {
      this.comprehensiveData.forEach((item, index) => {
        if (item.sortName === date.sortName) {
          const newValue =
            date.sort === '' ? 'desc' : date.sort === 'desc' ? 'asc' : '';
          this.$set(item, 'sort', newValue); // 响应式更新
        } else {
          this.$set(item, 'sort', ''); // 响应式更新
        }
      });
      this.orderSortName = date.order
      this.orderSort = date.sort
      
      if (this.orderSortName && this.orderSort) {
        // 排序
        this.tableData.sort((a, b) => {
          if (this.orderSort === 'asc') {
            return a[this.orderSortName] > b[this.orderSortName] ? 1 : -1
          } else if (this.orderSort === 'desc') {
            return a[this.orderSortName] < b[this.orderSortName] ? 1 : -1
          } else {
            return 0
          }
        })
      } else {
        this.tableData = JSON.parse(JSON.stringify(this.tableDataOrigin2))
      }
      
      this.$forceUpdate();
    },
     // 筛选项点击
     handelOptClick({ name, parent, value, selectType, ename }) {
        const index = this.checkBoxAttributeList.findIndex(
          (e) => e.name === name
        );
        const { selectValue, filterType } =
          this.checkBoxAttributeList[index] || {};
  
        let newV = selectValue;
  
        if (
          ename === 'gameAccountQufu' &&
          selectType === 3 &&
          !value.includes('|')
        ) {
          value = `${parent}|${value}`;
        }
  
        // 单选处理
        if (filterType !== 1) {
          if (selectValue.includes(value)) {
            newV = [];
          } else {
            newV = [value];
          }
        } else {
          // 多选处理 filterType=1 表示多选
          if (selectValue.includes(value)) {
            newV = selectValue.filter((e) => e !== value);
          } else {
            newV.push(value);
          }
        }
        this.$set(this.checkBoxAttributeList[index], 'selectValue', newV);
        this.$forceUpdate();
      },
    // 判断筛选项是否选中
    getOptIsCheck({ name, ename, selectType, parent, value }) {
        if (ename === 'gameAccountQufu' && selectType === 3) {
          value = parent + '|' + value;
        }
        return this.selectValueList().find((sV) => {
          return sV.value === value && sV.name === name;
        });
      },
      selectValueList() {
        console.log(1111);
        
        let a = []      // inputAttributeList
        this.checkBoxAttributeList.forEach((e) => {
          const arr = e.selectValue.map((item) => ({
            name: e.name,
            value: item,
            parent:
              e.ename === 'gameAccountQufu' && e.selectType === 3
                ? item.split('|')[0]
                : '',
          }));
          a = a.concat(arr);
        });
  
        return a;
      },
     //搜索关键词
     handelOptsSearch(v) {
      let result = [];
      if (v) {
        this.checkBoxAttributeList.forEach((e) => {
          if (e.selectType === 3) {
            // 级联数据
            for (let key in e.optionList) {
              const childList = e.optionList[key];
              childList.forEach((child) => {
                if (child.includes(v)) {
                  result.push({
                    name: e.name,
                    parent: key,
                    value: child,
                  });
                }
              });
            }
          } else {
            result = result.concat(
              e.optionList
                .filter((opt) => opt.includes(v))
                .map((opt) => ({ name: e.name, value: opt }))
            );
          }
        });
      }
      this.optsSearchResult = result;
    },
    getSeachConfig(data) {
      console.log(data, 222222);
      this.attributeData = JSON.parse(JSON.stringify(data));

      // 根据searchSortWeight排序
      const newCateList = data
        .filter((ele) => ele.type !== 0 && ele.searchType !== 0)
        .sort((a, b) => {
          const customA = JSON.parse(a.custom || '{}');
          const customB = JSON.parse(b.custom || '{}');
          return (
            ((customB && customB.searchSortWeight) || 0) -
            ((customA && customA.searchSortWeight) || 0)
          );
        });
      const cateList = data
        .filter(
          (ele) =>
            ele.type !== 0 &&
            ele.searchType !== 0 &&
            JSON.parse(ele.custom || '{}').showSearch !== false
        )
        .sort((a, b) => {
          return b.sort - a.sort;
        });
      // 复选框属性
      const checkBoxAttrGroup = [];
      this.checkBoxAttributeList = cateList
        .filter((e) => e.inputType !== 0) // 过滤掉输入框属性
        .map((e) => {
          const {
            inputList,
            selectType,
            name,
            searchType,
            nameGroup,
            custom,
            selectValue,
          } = e;

          let groupName = nameGroup || name;
          checkBoxAttrGroup.push(groupName);

          let optionList = inputList.split(','); // 子级数据
          let pOptionList = null; // 父级数据

          // 级联数据
          if (selectType === 3) {
            optionList = {};
            const treeData = JSON.parse(inputList);

            pOptionList = treeData.map((e) => e.parent_name);
            treeData.forEach((e) => {
              const { parent_name, childList } = e;
              optionList[parent_name] = childList;
            });
          }
          // 处理默认值
          let value = [];
          // if (name === '账号专区') {
          //   value = ['在售专区'];
          // }
          // value=this.getTagDetailSelectValue(name)||[]
          // if(this.tagDetail && Object.keys(this.tagDetail).length > 0){
          //   this.keyword=this.tagDetail.keyword
          //   this.tagDetail.attrValueList.forEach(item=>{
          //       if(item.name==name){
          //           value=item.selectValue
          //       }
          //   })
          // }
          if (name === '商品类型') {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('goodsType')) {
              value = optionList.includes(urlParams.get('goodsType'))
                ? [urlParams.get('goodsType')]
                : [];
            } else if (custom && custom !== '{}') {
              let newCustom = JSON.parse(custom);
              value = optionList.includes(newCustom.sdefault)
                ? [newCustom.sdefault]
                : [];
            } else {
              value = [optionList[0]];
            }
          }

          return {
            ...e,
            selectValue: selectValue || value,
            childValue: {}, // 只有级联数据才有
            hasClear: name != '商品类型' && searchType !== 3,
            optionList,
            pOptionList,
            defaultValue: value, // 清空选项时 可以设回初识默认值
            nameGroup: groupName,
            valueSearchType: e.valueSearchType || 'must',
          };
        });

      console.log(
        'checkBoxAttrGroup',
        [...new Set(checkBoxAttrGroup)],
        this.checkBoxAttributeList
      );
      this.checkBoxAttrGroup = [...new Set(checkBoxAttrGroup)];

      // 输入框属性搜索列表
      const inputAttributeList = cateList
        .filter((e) => e.inputType === 0 && e.name != '价格')
        .map((e) => {
          return {
            ...e,
            selectValue: e.selectValue || [],
            defaultValue: [], // 清空选项时 可以设回初识默认值
          };
        });

      // // 价格公共属性插入
      // this.inputAttributeList = [
      //   {
      //     'name': '价格',
      //     'selectType': 0,
      //     'inputType': 0,
      //     'inputList': '',
      //     'sort': 0,
      //     'filterType': 0,
      //     'searchType': 2,
      //     'type': 1,
      //     'searchSort': 0,
      //     selectValue: this.getTagDetailSelectValue('价格', data) || [],
      //     defaultValue: [],
      //   },
      // ].concat(inputAttributeList);

      // 排序数据
      // this.comprehensiveData = [
      //   // {
      //   //   sortName: '综合排序',
      //   //   selected: '',
      //   //   searchSort: 'score',
      //   //   value: '',
      //   // },
      //   // {
      //   //   sortName: '最多人看',
      //   //   selected: '',
      //   //   searchSort: 'gameSysinfoReadcount',
      //   // },
      //   {
      //     sortName: '上架时间',
      //     selected: '',
      //     searchSort: 'publishTime',
      //   },
      //   {
      //     sortName: '价格',
      //     selected: '',
      //     searchSort: 'price',
      //   },
      // ].concat(
      //   newCateList
      //     .filter((e) => {
      //       if (!e.searchSort) return false;
      //       const custom = e.custom ? JSON.parse(e.custom) : {};
      //       return Object.keys(custom).length > 0;
      //     })
      //     .map((e) => {
      //       return {
      //         sortName: e.name,
      //         selected: '',
      //         searchSort: e.searchSort,
      //       };
      //     })
      // );
      let list = [];
          data.forEach((ele) => {
            if (ele.type !== 0 && ele.searchType !== 0) {
              ele.moreTxt = '展开全部';
              list.push(ele);
            }
          });
          this.sortCateList = list;
          // this.formatClasData();
     
      return true;
    },
    showImagePriview(item){
      console.log(item);
      
      this.$refs.swperImagePriviewPage.showImagePriview(item)
    },
    getWzText(attrValueList,name,row) {
      if(row.productCategoryId!=82){
        return name
      }
      const newList = [...attrValueList].map((item) => ({
        name: item.name || item.productAttributeName,
        values: item.values || item.value.split(','),
      }));
      const titleList = GOOD_TITLE.concat(GOOD_TITLE_OTHER);
      const filterList = newList
        .filter((v) => {
          if (
            titleList.map((k) => k.label).includes(v.name) &&
            v.values &&
            v.values.length &&
            v.values[0] !== '0'
          ) {
            this.$set(
              v,
              'index',
              titleList.find((i) => i.label === v.name).index,
            );
            return v;
          }
        })
        .sort((a, b) => a.index - b.index);
      const titleInfo = filterList.map((v) => {
        let newName;
        if (v.name === '贵族等级') {
          newName = v.values[0].replace('贵族', 'V');
        } else if (GOOD_TITLE_OTHER.map((k) => k.label).includes(v.name)) {
          newName = v.values.length;
        } else {
          newName = v.values[0];
        }
        return (
          newName + titleList.find((i) => i.label === v.name).children.label
        );
      });
      return titleInfo.join(' ')+name;
    },
    sendGoodsCard(row) {
      // 获取当前会话类型（p2p 或 team）和对应的号
      const { store } = window.__xkit_store__ || {};
      if (!store || !store.uiStore || !store.uiStore.selectedSession) {
        this.$message.error('无法获取当前会话信息');
        return;
      }
      const selectedSession = store.uiStore.selectedSession; // 例如 team-123456 或 p2p-abcdef
      const tempList = selectedSession.split('-');
      const scene = tempList[0]; // 'team' 或 'p2p'
      tempList.shift();
      const to = tempList.join('-'); // 群号或p2p号
      this.onSendText2({ value: row.productSn, scene: scene, to: to });
      // 例如：发送商品卡片等
    },
    async onSendText2 (data){
            const { value = '', scene, to } = data;
            if (!value) {
              return;
            }
            const realText = value.trim();
            if (scene !== 'team' && /^[0-9a-zA-Z]+$/.test(realText)) {
              const params = {
                productSn: realText,
                ignore: 1,
              };
              const res = await detailBySn(params);
              if (res && res.code == 200 && res.data) {
                const data = res.data;
                data.product.subTitle=data.product.subTitle.substring(0,30)
                data.product.detailTitle==data.product.detailTitle.substring(0,30)
                data.product.name==data.product.name.substring(0,30)
                // return
                const attach = util.createProdcutAttach(data);
                const { store } = window.__xkit_store__;
                const myAccount = store.userStore.myUserInfo.account;
                store.msgStore
                  .sendCustomMsgActive({
                    scene: scene,
                    from: myAccount,
                    to: to,
                    attach: JSON.stringify(attach),
                  })
                  .then((res) => {
                    this.$message.success("发送成功")
                    // 让消息滚动到可视区域
                    document.getElementById(`${res.idClient}`).scrollIntoView();
                  })
                  .catch((err) => {
                    this.$message.error("发送失败")
                    console.log('发送失败', err);
                  });
              } else {
                this.sendTxt(data);
              }
            } else {
              this.sendTxt(data);
            }
          },
    goSameProduct(){
     
      let arr = this.checkBoxAttributeList
        .filter(item => item.selectValue.length)
        .map(item => {
          return {
            ...item,
            value: item.selectValue.join(',')
          }
        })
      // if(!this.wzrySameProductCode){
      //   this.$message.warning('请输入商品编号')
      //   return
      // }
      let data={
        attrValueList:arr
      }
      if(this.inputAttributeList.length){
        let queryIntParams=[]
        this.inputAttributeList.forEach(item=>{
          if(item.selectValue.length){
            queryIntParams.push({
              min:item.selectValue[0]||0,
              max:item.selectValue[1]||9999999,
              key:item.key
            })
          }
          
        })
        data.queryIntParams=queryIntParams
      }
      // if(this.orderSort){
      //   data.order=this.orderSortName,
      //   data.sort=this.orderSort
      // }
      getWzrySameProductList({offDay:100,productSn:this.wzrySameProductCode},data).then(res=>{
        if(res.code==200){
          this.tableDataOrigin2=res.data.list
          // 赋值之前先排序
          let sortedList = JSON.parse(JSON.stringify(res.data.list))
          if (this.orderSortName && this.orderSort) {
            sortedList = [...sortedList].sort((a, b) => {
              if (this.orderSort === 'asc') {
                return a[this.orderSortName] > b[this.orderSortName] ? 1 : -1
              } else if (this.orderSort === 'desc') {
                return a[this.orderSortName] < b[this.orderSortName] ? 1 : -1
              } else {
                return 0
              }
            })
          }
          this.tableData = sortedList
        }
      })
    },
    doRefresh() {
      const { store } = window.__xkit_store__;
      const selectedSession = store.uiStore.selectedSession;
      const tempList = selectedSession.split('-');
      const scene = tempList[0];
      tempList.shift();
      const to = tempList.join('-');
      const teamId = parseInt(to);
      let loadingInstance1 = Loading.service({ fullscreen: true });
      this.getOrderDetail(teamId);
      setTimeout(() => {
        loadingInstance1.close();
      }, 200);
    },
    doSendBaopeiMsg2Team() {
      const data = {
        flowTeamId: this.flowId,
        memberId: this.sellerId
      };
      sendBaopeiMsg2Team(data).then(res => {
        if (res.code == 200) {
        }
      });
    },
    goMsgChat(item){
      if(item.imStatus==2){
        inTeamAndActive({teamId:this.teamIdChat}).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.message
          });
          this.actionCardClickChange()
          // this.getHisTeamList()
        }
      });
      }else{
        this.actionCardClickChange()
      }
    
    },
    handleSearch(){
      
      this.getHisTeamList()
      
    },
    getHisTeamList(){
      this.pageNum = 1;
      let apiFunc = null;
      if (this.axiosType === 'team-history') {
        apiFunc = getHisTeamList;
      } else if (this.axiosType === 'team-yijia') {
        apiFunc = getHisNegoTeamList;
      }
      if (apiFunc) {
        apiFunc({ pageSize: 30, pageNum: 1, keyword: this.keyword }).then(res => {
          if (res.code == 200) {
            this.teamListData = res.data.list;
            this.teamTotal = res.data.total;
          }
        });
      }
    },
     // 滚动事件处理函数
     handleScroll() {
      // 通过 ref 获取滚动容器 DOM 元素
      const container = this.$refs.scrollContainer;
      
      // 计算关键值
      const scrollTop = container.scrollTop;         // 已滚动高度
      const containerHeight = container.clientHeight; // 容器可视高度
      const contentHeight = container.scrollHeight;  // 内容总高度
      
      // 判断是否滚动到底部（预留 200px 缓冲距离，可根据需求调整）
      if (scrollTop + containerHeight >= contentHeight - 200 && !this.isLoading&&this.pageNum*30<this.teamTotal) {
        this.isLoading = true; // 标记加载状态，防止重复请求
        this.loadMoreData();    // 执行加载更多数据的逻辑
      }
    },
      loadMoreData() {
      this.pageNum++; 
      let apiFunc = null;
      if (this.axiosType === 'team-history') {
        apiFunc = getHisTeamList;
      } else if (this.axiosType === 'team-yijia') {
        apiFunc = getHisNegoTeamList;
      }
      if (apiFunc) {
        apiFunc({pageSize:this.pageSize,pageNum:this.pageNum,keyword:this.keyword}).then(res=>{
          if(res.code==200){
            this.teamListData = this.teamListData.concat(res.data.list);
            this.isLoading = false;
            this.teamTotal=res.data.total
          }
        })
      }
    },
    teamListClick(item){
      this.teamDetail=item
      this.teamListClickId=item.id
      this.teamIdChat=item.teamId
      this.newFlowId=item.flowId
      getFlowState(item.teamId).then(res => {
          const result = res.data || {};
          this.newFlowId = result.id;
          this.newSidim = result.sellerim;
          this.newBidim = result.buyerim;
          if (result.orderId) {
            getOrderDetail(result.orderId).then(res => {
              this.newOrderDetail = res.data;
              this.newHasOrderDetail = true;
            });
          } else {
            this.newHasOrderDetail = false;
            this.newOrderDetail = {};
          }
          getMemberDetail(result.sellerId).then(res => {
            this.$store.dispatch('setSidMember', res.data);
          });
          getMemberDetail(result.buyerId).then(res => {
            this.$store.dispatch('setBidMember', res.data);
          });

          this.newTeamNote = result.teamNote;
          this.newTeamPnote = result.teamPnote;
          this.newTeamInfo = result;
          if (result.processFlow) {
            this.newFlow = JSON.parse(result.processFlow);
            this.newHasFlow = true;
          }
        });
      chatMessages({teamId:item.teamId}).then(res=>{
        if(res.code==200){
          this.teamMsgList=res.data
        }
      })
    },
    luhaoSubmit(){
      if(!this.gameValue){
          this.$message.error('请选择游戏')
          return
      }
      let obj=this.luhaoGameList.filter(item=>{
        return item.id==this.gameValue
      })
      window.open(window.location.origin+`/#/pushAccount/pushAccount?productCategoryId=${obj[0].id}&attriCateId=${obj[0].attriCateId}&type=push`, '_blank');
      this.selectGameFlag=false
    },
    showCreateheTong(e){

      this.actionObj={}
      this.actionData={}
      this.hetongType=e
      this.showHetong=true
    },
    changePwd1(v){
      this.adminForm.pwd1=v
    },
    changePwd0(v){
      this.adminForm.pwd0=v
    },
    changeUserName(v){
      this.adminForm.userName=v
    },
    handleDialogConfirmPwd() {
      if (!this.adminForm.userName) {
        this.$message({
          message: '请输入用户账号',
          type: 'error'
        });
        return;
      }
      if (!this.adminForm.pwd0) {
        this.$message({
          message: '请输入密码',
          type: 'error'
        });
        return;
      }
      if (this.adminForm.pwd0 !== this.adminForm.pwd1) {
        this.$message({
          message: '两次密码不一致，请重新输入',
          type: 'error'
        });
        return;
      }
      this.$confirm('是否要确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updatePassword2(
          {},
          {
            password: this.adminForm.pwd0,
            pass2: this.adminForm.pwd1,
            username:this.adminForm.userName
          }
        ).then(res => {
          if(res.code==200){
            this.$message({
            message: '密码修改成功',
              type: 'success'
            });
            this.passwordVisible = false;
          }
        });
      });
    },
    resetPassword(){
      this.adminForm={
        pwd0:'',
        pwd1:'',
        userName:''
      }
      this.passwordVisible=true
    },
    handelStatusChange(value) {
      changeStatus(value).then((res) => {
        if (res.code == 200) {
          this.kfStatus = value;
        }
      });
    },
    getType(state) {
      if (state == 'PERSON_MEMBER_WAIT') {
        return 'warning';
      } else if (state == 'PERSON_DONE') {
        return 'success';
      } else {
        return 'error';
      }
    },
    changeStaticsType() {
      this.todoListFed = [];
      if (this.staticsType == 'wait') {
        personChatTodoList({
          pageNum: 1,
          pageSize: 999,
          productSn:this.searchFed.trim()
        }).then((res) => {
          if (res.code == 200) {
            this.originalTodoList = res.data.list;
            this.todoList = [...this.originalTodoList];
          }
        });
      } else if (this.staticsType == 'finish') {
        personChatDoneList({
          pageNum: 1,
          pageSize: 200,
          productSn:this.searchFed.trim()
        }).then((res) => {
          if (res.code == 200) {
            this.originalTodoList = res.data.list;
            this.todoList = [...this.originalTodoList];
          }
        });
      }
    },
    doSearchFed() {
      this.changeStaticsType()
      // const trimmedSearchFed = this.searchFed.trim().toLowerCase(); 
      // if (trimmedSearchFed === '') {
      //   this.onRefresh();
      // } else {
      //   let list = JSON.parse(JSON.stringify(this.originalTodoList));
      //   this.todoList = list.filter((ele) => {
      //     const { teamPnote = '' } = ele;
      //     return teamPnote.toLowerCase().includes(trimmedSearchFed); 
      //   });
      // }
    },
    clearSearchFed(){
      this.changeStaticsType()
    },
    refreshList(list) {
      this.originalTodoList = list;
      this.todoList = [...list];
    },
    doEdit(item) {
      this.imteamId = item.id;
      this.teamPnote = item.teamPnote;
      this.showEditDialog = true;
    },
    handleBuyerIm(imaccount, item) {
      this.goChat(imaccount);
      this.chatClick(item);
    },
    chatClick(item) {
      let data = {
        imteamId: item.id,
      };
      personChatClick(data).then((res) => {
        if (res.code == 200) {
          this.originalTodoList = res.data.list;
          this.todoList = [...this.originalTodoList];
        }
      });
    },
    onRefresh() {
      this.changeStaticsType();
    },
    getTeamState(state) {
      if (state == 'PERSON_MEMBER_WAIT') {
        return '待服务';
      } else if (state == 'PERSON_DONE') {
        return '已完成';
      } else {
        return '服务中';
      }
    },
    showStatics2() {
      this.showStatics2Layer = true;
      this.staticsType = 'wait';
      this.changeStaticsType();
    },
    showStatics() {
      this.showStaticsDialog = true;
    },
    onSendKF() {
      this.showSendKF = true;
    },
    onFindUser() {
      this.showFindUser = true;
    },
    showYijia() {
      this.showYijiaModal = true;
    },
    doRecordTeam() {
      this.$refs.recordTeamForm.validate((valid) => {
        if (valid) {
          consultRecordsCreate(this.recordTeamForm)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
              }
            })
            .finally(() => {
              this.showRecordTeam = false;
            });
        }
      });
    },
    showChangeIMTeamName() {
      this.showChangeIMTeamNameModal = true;
    },
    closeKfList() {
      this.showKfList = false;
    },
    addPerson() {
      this.showKfList = true;
      if(this.$refs.kfListRef){
        this.$refs.kfListRef.resetForm()
      }
     
    },

    doShowRecordTeam() {
      const { store } = window.__xkit_store__;
      const im = store.uiStore.selectedSession.replace('team-', '');
      this.recordTeamForm.flowTeamId = this.flowId;
      this.recordTeamForm.consultTo = im;
      this.recordTeamForm.consultContent = '';
      this.showRecordTeam = true;
    },
    doShowRecordP2p() {
      const { store } = window.__xkit_store__;
      const im = store.uiStore.selectedSession.replace('p2p-', '');
      loadByIM({ im }).then((res) => {
        if (res.code == 200) {
          this.recordP2pForm.userName = res.data.username;
          this.showRecordP2p = true;
        }
      });
    },
    doRecordP2p() {
      const { store } = window.__xkit_store__;
      this.recordP2pForm.consultTo = store.uiStore.selectedSession.replace(
        'p2p-',
        ''
      );
      consultRecordsCreate(this.recordP2pForm)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message);
          }
        })
        .finally(() => {
          this.showRecordP2p = false;
        });
    },
    goChat(imaccount) {
      const { store } = window.__xkit_store__;
      const sessionId = `p2p-${imaccount}`;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', imaccount);
      }
    },
    goChatByUserNameReal() {
      this.$refs.goChatByUserNameForm.validate((valid) => {
        if (valid) {
          getImaccount2({
            keyword: this.goChatByUserNameForm.username,
          }).then((res) => {
            if (res.code == 200) {
              this.showGoChatByUserName = false;
              const imaccount = res.data.imaccount;
              const { store } = window.__xkit_store__;
              const sessionId = `p2p-${imaccount}`;
              if (store.sessionStore.sessions.get(sessionId)) {
                store.uiStore.selectSession(sessionId);
              } else {
                store.sessionStore.insertSessionActive('p2p', imaccount);
              }
            }
          });
        }
      });
    },
    goChatByUserName() {
      this.showGoChatByUserName = true;
    },
    hideDingdan2() {
      this.dialogVisible2 = false;
    },
    hideDingdan1() {
      this.dialogVisible1 = false;
    },
    doCall() {
      const { memberIM, callType } = this.callDialogForm;
      let data = {
        memberIM,
        taskId: callType,
        from: memberIM,
        memberRole: 'member',
      };

      soundCall(data).then((res) => {
        if (res.code == 200) {
          this.$message.success('呼叫成功');
          this.callDialog = false;
        }
      });
    },
    callUserPhone() {
      const { store } = window.__xkit_store__;
      const memberIM = store.userStore.myUserInfo.account;
      kferCallBar({
        memberIM,
      }).then((res) => {
        if (res.code == 200) {
          var sdk = document.createElement('script');
          sdk.async = !0;
          sdk.src = res.data;
          sdk.onload = function () {
            //sdk加载成功后，注册呼叫工具条事件
            qiConnect.on({
              onload: function () {
                //呼叫工具条加载完毕的事件通知。此事件完成后才可调用外呼接口
                console.log('呼叫工具条加载完毕！');
              },
              /**
               * 建立新呼叫会话的事件，包括外呼和呼入
               * @param  {Object} session  呼叫会话的session信息
               *
               * @param  {String} session.address      号码归属地
               * @param  {String} session.usernumber   客户号码
               * @param  {Number} session.sessionid    会话id
               * @param  {String} session.staffnumber  坐席号码
               * @param  {Number} session.staffid      坐席id
               * @param  {String} session.staffname    坐席账号
               * @param  {Number} session.direction    会话方向，1表示呼入，2表示呼出
               */
              session: function (session) {
                console.log('session', session);
              },
            });
          };
          document.body.appendChild(sdk);
        }
      });
    },
    callWeChat() {
      const { store } = window.__xkit_store__;
      const memberIM = store.uiStore.selectedSession.replace('p2p-', '');
      wxMsgCall({
        memberIM,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.data);
        }
      });
    },
    callUser() {
      const { store } = window.__xkit_store__;
      const memberIM = store.uiStore.selectedSession.replace('p2p-', '');
      this.callDialog = true;
      this.callDialogForm.memberIM = memberIM;
    },
    // sidimClick(index) {
    //   if (index == 1) {
    //     this.soundCall('sidim');
    //   } else {
    //     this.goDialog('sidim');
    //   }
    // },
    callClick(index) {
      if (index == 1) {
        this.callUserPhone();
      }
      if (index == 3) {
        this.callWeChat();
      } else {
        this.callUser();
      }
    },
    // bidimClick(index) {
    //   if (index == 1) {
    //     this.soundCall('bidim');
    //   } else {
    //     this.goDialog('bidim');
    //   }
    // },
    soundCall(key) {
      const memberIM = this[key];
      this.callDialogForm.memberIM = memberIM;
      this.callDialog = true;
    },
    rightBoxSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const formData = {
            'albumPics': '',
            'albumPicsJson': '[]',
            'productAttributeValueList': [],
            'brandId': '',
            'productAttributeCategoryId': '',
            'gameAccountQufu': this.ruleForm.gameAccountQufu.join('/'),
            'description': '',
            'qcsy2': 0,
            'pic': '',
            'qcsy3': 0,
            'selectProductPics': [],
            'price': this.ruleForm.price,
            'originalPrice': '',
            'stock': 9,
            'gameGoodsFangxin': 0,
            'gameGoodsBukuan': 0,
            'gameGoodsJiangjia': 0,
            'gameGoodsYijia': 0,
            'sort': '',
            'publishStatus': 0,
            'gameGoodsYishou': 1,
            'gameGoodsYuyue': 0,
            'gameGoodsSaletype': 1,
            'gameCareinfoPhone': '',
            'gameCareinfoTime': '',
            'gameCareinfoVx': '',
            'subjectProductRelationList': [],
            'loginType': '',
            'productCategoryId': '',
            'username': this.ruleForm.username,
            'productCategoryName': '逆水寒手游',
            'productSn': this.ruleForm.productSn || '',
          };
          let productAttributeValueList = [];
          productAttributeListall().then((res) => {
            res.data.forEach((item) => {
              productAttributeValueList.push({
                productAttributeId: item.id,
                value:
                  item.name === '游戏账号'
                    ? this.ruleForm.account
                    : item.name === '区服'
                    ? this.ruleForm.gameAccountQufu.join('/')
                    : '',
                attriName: item.name,
                sort: item.sort,
                filterType: item.filterType,
                searchType: item.searchType,
                type: item.type,
                searchSort: item.searchSort,
              });
            });
            let params = {
              ...formData,
              productAttributeValueList: productAttributeValueList,
            };
            taskCreate(params).then((res) => {
              if (res.code == 200) {
                this.loadingFlag = true;
                // let time = setInterval(() => {}, 3000);
                this.stl = setInterval(() => {
                  recordTaskDetail(res.data.id).then((res) => {
                    if (res.code == 200 && res.data && res.data.propertyBag) {
                      const obj = JSON.parse(res.data.propertyBag);
                      if (obj.state.includes('need_show_')) {
                        obj.state = `${obj.state}_done`;
                        this.showStateImg(obj.state_img);
                        this.loadingFlag = false;
                      } else if (
                        obj.state === 'login_success' ||
                        obj.state === 'login_timeout' ||
                        obj.state === 'login_fail'
                      ) {
                        this.luhaosuccess = true;
                        this.stl = clearInterval(this.stl);
                        this.$message.success('开始录号');
                      }
                    }
                  });
                }, 2000);
              }
            });
            // let time = setInterval(() => {}, 3000);
            // clearInterval(time);
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    showStateImg(img) {
      this.stateImg = img;
    },
    btnClick() {
      this.gameValue=''
      this.selectGameFlag=true
      // const { store } = window.__xkit_store__;
      // const im = store.uiStore.selectedSession.replace('p2p-', '');
      // productAttributeListall().then((res) => {
      //   let list = res.data.filter((item) => {
      //     return item.name == '区服';
      //   });

      //   let newList = [];
      //   JSON.parse(list[0].inputList).forEach((item) => {
      //     const parentOption = {
      //       value: item.parent_name,
      //       label: item.parent_name,
      //       children: [],
      //     };
      //     item.childList.forEach((child) => {
      //       const childOption = {
      //         value: child,
      //         label: child,
      //       };
      //       parentOption.children.push(childOption);
      //     });
      //     newList.push(parentOption);
      //   });
      //   this.optionsList = newList;
      // });
      // loadByIM({ im }).then((res) => {
      //   if (res.code == 200) {
      //     this.ruleForm = {
      //       username: res.data.phone,
      //       productSn: '',
      //       account: '',
      //       price: '',
      //       gameAccountQufu: [],
      //     };
      //     this.stateImg = '';
      //     this.luhaosuccess = false;
      //     this.showRightBox = true;
      //   }
      // });
    },
    createBukuan() {
      this.createBukuanLoading = true;
      const data = Object.assign(
        {
          buyType: 0,
        },
        this.bukuanForm
      );
      generateKKOrderByKF(data)
        .then((res) => {
          if (res.code == 200) {
            this.bukuanUrl = res.data;
          }
        })
        .finally((f) => {
          this.createBukuanLoading = false;
        });
    },
    showBukuan() {
      this.bukuanForm = {
        sellerUsername: '',
        buyerUsername: '',
        price: '',
        cateId: '',
      };
      Promise.all([
        fetchList(74, {
          pageNum: 1,
          pageSize: 999,
        }),
      ]).then((values) => {
        this.gameList = [];
        values.forEach((item) => {
          this.gameList = this.gameList.concat(item.data.list);
        });
        this.bukuanDialog = true;
      });
    },
    onCloseView() {
      this.showViewer = false;
    },
    changeModel(type) {
      this.axiosType = type;
      if((type=='team-history'||type === 'team-yijia')&&this.flagPage){
        this.getHisTeamList()
        this.teamListClickId=''
        this.teamMsgList=[]
        this.teamDetail={}
        // 修改全局v-modal样式
        // 使用setTimeout确保在DOM更新后再执行
        setTimeout(() => {
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.addedNodes.length) {
                const vModals = document.querySelectorAll('body .v-modal');
                if(vModals.length) {
                  vModals.forEach(el => {
                    el.style.zIndex = 'auto';
                    // 强制覆盖样式
                    el.setAttribute('style', 'z-index: auto !important');
                  });
                }
              }
            });
          });
          
          observer.observe(document.body, {
            childList: true,
            subtree: true
          });

          // 立即执行一次,处理已存在的v-modal
          const existingVModals = document.querySelectorAll('body .v-modal');
          if(existingVModals.length) {
            existingVModals.forEach(el => {
              el.style.zIndex = 'auto';
              el.setAttribute('style', 'z-index: auto !important');
            });
          }
        }, 0);
      } else {
        this.teamMsgList=[]
        this.teamDetail={}
        // 修改全局v-modal样式
        // 使用setTimeout确保在DOM更新后再执行
        setTimeout(() => {
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.addedNodes.length) {
                const vModals = document.querySelectorAll('body .v-modal');
                if(vModals.length) {
                  vModals.forEach(el => {
                    el.style.zIndex = 'auto';
                    // 强制覆盖样式
                    el.setAttribute('style', 'z-index: 2005');
                  });
                }
              }
            });
          });
          
          observer.observe(document.body, {
            childList: true,
            subtree: true
          });

          // 立即执行一次,处理已存在的v-modal
          const existingVModals = document.querySelectorAll('body .v-modal');
          if(existingVModals.length) {
            existingVModals.forEach(el => {
              el.style.zIndex = 'auto';
              el.setAttribute('style', 'z-index: 2005');
            });
          }
        }, 0);
      }
      if(this.flagPage){
        this.type = type;
        myStore.setSessionType(type);
      }
      if(!this.flagPage||(!this.flagPage&&(type=='team-history'||type === 'team-yijia'))){
        this.type = 'all';
        myStore.setSessionType('all');
      }
     
    },
    actionCardClickChange1(){
      this.flagPage=false
      this.changeModel('all')
      setTimeout(()=>{
        this.flagPage=true
      })
    },
    actionCardClickChange(){
      this.flagPage=false
      this.changeModel('all')
      setTimeout(()=>{
        this.flagPage=true
      const imaccount =this.teamIdChat;
              //根据群号进入会话
              const { store } = window.__xkit_store__;
              const sessionId = `team-${imaccount}`;
              if (store.sessionStore.sessions.get(sessionId)) {
                store.uiStore.selectSession(sessionId);
              } else {
                store.sessionStore.insertSessionActive('team', imaccount);
              }
      
      })

      
   
      // setTimeout(()=>{
      //   const { store } = window.__xkit_store__;
      //   // 使用teamId作为群号
      //   const sessionId = `team-${this.teamDetail.teamId}`;
      //   if (store.sessionStore.sessions.get(sessionId)) {
      //     store.uiStore.selectSession(sessionId);
      //   } else {
      //     store.sessionStore.insertSessionActive('team', this.teamDetail.teamId);
      //   }
      // },100)
    },
    searchOrder() {
      createSellerFormURL({
        iscover: this.postFormOrder.isCover,
        ensure: 1,
        orderSn: this.postFormOrder.orderId,
      }).then((res) => {
        let { data } = res;
        data = encodeURIComponent(data);
        this.orderUrl = `https://m1.kkzhw.com/pages/form/form?token=${data}`;
      });
    },
    doRecordByKFDialog() {
      this.$refs.recordForm.validate((valid) => {
        if (valid) {
          const { store } = window.__xkit_store__;
          this.postFormRecord.consultTo = store.uiStore.selectedSession.replace(
            'p2p-',
            ''
          );
          consultRecordsCreate(this.postFormRecord)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
              }
            })
            .finally(() => {
              this.showRecordByKFDialog = false;
            });
        }
      });
    },
    showCreateTimeout() {
      this.createTimeoutDialog = true;
      this.timeoutEnd = new Date();
    },
    timeoutSubmit() {
      const data = {
        teamId: this.teamId,
        timeMin: new Date(this.timeoutEnd).getTime(),
        note: this.timeoutWaringTxt,
      };
      actionTimeClockSet(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('创建提醒成功');
          this.createTimeoutDialog = false;
        }
      });
    },
    showRecordByKF() {
      this.postFormRecord = {
        consultContent: '',
        consultTo: '',
        consultType: 0,
        productSn: '',
      };
      this.showRecordByKFDialog = true;
    },
    showOrderDialog() {
      this.orderDialog = true;
    },
    showCreateHuishou() {
      if (this.isTeam) {
        this.dialogVisible2 = true;
      } else {
        this.dialogVisible1 = true;
      }
    },
    beSimilarGoods(){
      this.tableData=[]
      this.tableDataOrigin2=[]
      this.optsSearchResult=[]
      this.wzrySameProductCode=""
      this.keyword2=""
      getProductAttributeAttrInfo(82).then(
        (response) => {
          if (response.code == 200) {
            return this.getSeachConfig(response.data);
          }
        }
      );
      this.dialogGoodsVisible=true
    },
    // showCreateDanbaoByKF(productCategoryId) {
    //   this.postForm = {
    //     cateId: '',
    //     sellerUsername: '',
    //     buyerUsername: '',
    //     kfIM: ''
    //   };
    //   this.dialogVisible = true;
    //   Promise.all([
    //     fetchList(74, {
    //       pageNum: 1,
    //       pageSize: 999
    //     })
    //   ]).then(values => {
    //     this.gameList = [];
    //     values.forEach(item => {
    //       this.gameList = this.gameList.concat(item.data.list);
    //     });
    //     if (productCategoryId) {
    //       // 有产品预填游戏
    //       this.postForm.cateId = productCategoryId;
    //     }
    //   });
    // },
    // doCreateDanbaoByKF() {
    //   this.dialogVisible = false;
    //   const { store } = window.__xkit_store__;
    //   this.postForm.kfIM = store.userStore.myUserInfo.account;
    //   createDanbaoByKF(this.postForm).then(res => {
    //     if (res.code == 200) {
    //       this.$message.success('创建成功');
    //     }
    //   });
    // },
    showCreateTeam() {
      this.postForm3 = {
        teamType: '',
        cateId: '',
        sellerUsername: '',
        buyerUsername: '',
        kfIM: '',
      };
      this.dialogVisible3 = true;
      Promise.all([
        fetchList(74, {
          pageNum: 1,
          pageSize: 999,
        }),
      ]).then((values) => {
        this.gameList = [];
        values.forEach((item) => {
          this.gameList = this.gameList.concat(item.data.list);
        });
      });
    },
    doCreateTeamByKF() {
      this.dialogVisible3 = false;
      const { store } = window.__xkit_store__;
      this.postForm3.kfIM = store.userStore.myUserInfo.account;
      createTeamByKF(this.postForm3).then((res) => {
        if (res.code == 200) {
          this.$message.success('创建成功');
        }
      });
    },
    handleTools(item) {
      const that = this;
      const { step_code } = item;
      if (step_code == 'CONTRACT_DRAW') {
        this.$confirm('请选择给卖家打款方式', '提示', {
          cancelButtonText: '支付宝',
          confirmButtonText: '银行卡',
          type: 'warning',
          distinguishCancelAndClose: true, //区分取消与关闭
        })
          .then(() => {
            request({
              url: item.url,
              method: 'get',
              params: {
                formValue: '0',
              },
            }).then((res) => {
              if (res.code == 200) {
                that.$message.success('卡片发送成功');
              }
            });
          })
          .catch((action) => {
            if (action == 'cancel') {
              if (item.url)
                request({
                  url: item.url,
                  method: 'get',
                  params: {
                    formValue: '2',
                  },
                }).then((res) => {
                  if (res.code == 200) {
                    that.$message.success('卡片发送成功');
                  }
                });
            }
          });
      } else if (step_code == 'FLOW_CHECK') {
        this.$confirm(
          '客服确认卖家是否进行包赔认证，确认卖家已完成包赔认证后再开始验号！',
          '提示',
          {
            cancelButtonText: '沟通卖家包赔',
            confirmButtonText: '确认继续',
            type: 'warning',
            distinguishCancelAndClose: true, //区分取消与关闭
          }
        )
          .then(() => {
            if (item.url) {
              request({
                url: item.url,
                method: 'get',
              });
            }
          })
          .catch((action) => {
            if (action == 'cancel') {
              const findIt = that.tools.find(
                (ele) => ele.step_code == 'SELLER_BAOPEI_FORM'
              );
              request({
                url: findIt.url,
                method: 'get',
              }).then((res) => {
                if (res.code == 200) {
                  that.$message.success('发送包赔确认成功');
                }
              });
            }
          });
      } else {
        if (item.url) {
          request({
            url: item.url,
            method: 'get',
          }).then((res) => {
            if (res.code == 200) {
              this.$emit('toolsClick', res);
            }
          });
        }
      }
    },
    toggleMute() {
      const { store } = window.__xkit_store__;
      const session = store.uiStore.selectedSession;
      const teamId = session.replace('team-', '');
      const myAccount = store.userStore.myUserInfo.account;
      let type = '0';
      if (this.muteTxt === '开启免打扰') {
        type = '1';
      } else {
        type = '0';
      }
      let data = {
        teamId,
        kferIM: myAccount,
        type,
      };
      setTeamQuiet(data).then((res) => {
        if (res.code == 200) {
          this.$message.success('设置成功');
          if (this.muteTxt == '开启免打扰') {
            this.muteTxt = '关闭免打扰';
          } else {
            this.muteTxt = '开启免打扰';
          }
        }
      });
    },
    // goDialog(key) {
    //   const { store } = window.__xkit_store__;
    //   const sessionId = `p2p-${this[key]}`;
    //   if (store.sessionStore.sessions.get(sessionId)) {
    //     store.uiStore.selectSession(sessionId);
    //   } else {
    //     store.sessionStore.insertSessionActive('p2p', this[key]);
    //   }
    // },
    // getMember(sid, bid) {
    //   if (!this.$store.getters.roles.includes('客服')) {
    //     return;
    //   }
    //   getMemberDetail(sid).then(res => {
    //     this.sidim = res.data.imaccount;
    //     store.dispatch('setSidMember', res.data);
    //   });
    //   getMemberDetail(bid).then(res => {
    //     this.bidim = res.data.imaccount;
    //     store.dispatch('setBidMember', res.data);
    //   });
    // },
    getOrderDetail() {
      this.$emit('getOrderDetail', this.teamId);
    },
    customRender() {
      this.$uikit.render(
        SearchContainer,
        {
          onClickChat: () => {
            this.model = 'chat';
          },
        },
        this.$refs.search
      );
      this.$uikit.render(
        AddContainer,
        {
          onClickChat: () => {
            this.model = 'chat';
          },
        },
        this.$refs.add
      );
      this.$uikit.render(MyAvatarContainer, null, this.$refs.avatar);

      this.$uikit.render(ConversationContainer, null, this.$refs.conversation);

      this.$uikit.render(
        ChatContainer,
        {
          // 以下是自定义渲染，用 compile 函数包裹 html 就可以了，注意 class 要写成 className
          // 安装并引入： import { compile } from "jsx-web-compiler";
          renderHeader: (options) => {
            if (options.id.indexOf('team-') === 0) {
              this.isTeam = true;
              this.$emit('isTeamChange', true);
            } else {
              this.isTeam = false;
              this.$emit('isTeamChange', false);
            }
            return null;
          },
          renderMessageName: (msg) => {
            const { store } = window.__xkit_store__;
            const { from: account, sessionId: teamId, scene, to } = msg;
            const name = store.uiStore.getAppellation({ account, teamId });
            let imnick = '';
            if (scene == 'team') {
              try {
                const team = store.teamStore.teams.get(to);
                const { serverExt = {} } = team;
                let obj = JSON.parse(serverExt);
                const { sim, bim } = obj;
                if (account == sim) {
                  imnick = '卖家';
                } else if (account == bim) {
                  imnick = '买家';
                }
              } catch (err) {
                console.log(err);
              }
            }
            return compile(
              `<div className="name">
            <span>{context.name}</span>
            {context.imnick?(
              context.imnick=="买家"?
              (<span className="gfmsg bname">{context.imnick}</span>):(<span className="gfmsg sname">{context.imnick}</span>)
            ):('')}
            </div>`,
              { name, imnick }
            );
          },
          renderEmpty: () =>
            compile(`<div className="empty_box">
            <img className='empty' src='https://yx-web-nosdn.netease.im/common/2946c48f29d747305e68b1ddf27f3472/无成员可添加@3x.png'/>
            <div className='welcome'>欢迎使用看看账号</div>
            </div>`),
          renderTeamCustomMessage: (options) => {
            const { msg } = options;
            const { type } = msg;
            if (type === 'notification') {
              return compile(`<div className="hide"></div>`);
            }
            if (type === 'tip') {
              return compile(`<div>
              <div className='common-parse-session-noti'>
                <span className='lingdan'></span>
                ${msg.body}
                </div>
              </div>`);
            }
          },
          renderP2pCustomMessage: (options) => {
            const { msg } = options;
            const { type } = msg;
            if (type === 'notification') {
              return compile(`<div className="hide"></div>`);
            }
            if (type === 'tip') {
              return compile(`<div>
              <div className='common-parse-session-noti'>
                <span className='lingdan'></span>
                ${msg.body}
                </div>
              </div>`);
            }
          },
          renderMessageAvatar: (msg) => {
            const { store } = window.__xkit_store__;
            const isSelf = msg.from === store.userStore.myUserInfo.account;
            if (isSelf) {
              return compile(`<context.MyAvatarContainer canClick={false} />`, {
                MyAvatarContainer,
              });
            } else {
              return compile(
                `<context.ComplexAvatarContainer
                   account={context.msg.from}
                   canClick={false}
                 />`,
                { ComplexAvatarContainer, msg }
              );
            }
          },
          renderMessageOuterContent: (msg) => {
            const { store, nim } = window.__xkit_store__;
            if (msg.type === 'custom') {
              msg.attach = msg.attach || {};
            }
            if (msg.type === 'custom' && msg.attach.type === 'kkmsg_action2') {
              const msgText = msg.attach.body.title;
              const content = msg.attach.body.content;
              const actions = msg.attach.body.action || [];
              const data = msg.attach.data || {};
              const handleActionClick = this.handleActionClick;
              const canShow = this.canShow;
              return compile(
                `<div className="kk_custom_msg_wrapper kk_custom_msg_wrapper2">
                  <div className="kk_custom_msg_title" dangerouslySetInnerHTML={{ __html: context.msgText }}></div>
                  <div className="kk_custom_msg_content"
                  dangerouslySetInnerHTML={{ __html: context.content }}></div>
                  <div className="action_box">
                    {context.actions.filter(action => context.canShow(action)).map((action, index) => (
                    <div className="kk_custom_msg_action"
                    key={index}
                    onClick={()=>{context.handleActionClick(action,context.data)}}>{action.name}</div>
                  ))}
                  </div>
              </div>`,
                { msgText, content, actions, handleActionClick, canShow, data }
              );
            } else if (
              msg.type === 'custom' &&
              msg.attach.type === 'kkmsg_action'
            ) {
              let msgText = msg.attach.body.title;
              let content = msg.attach.body.content;
              const actions = msg.attach.body.action || [];
              const data = msg.attach.data || {};
              const handleActionClick = this.handleActionClick;
              const canShow = this.canShow;
              if (msg.scene == 'team') {
                const team = store.teamStore.teams.get(msg.to);
                const { serverCustom = '{}' } = team;
                const serverCustomJson = JSON.parse(serverCustom);
                const { bim, sim } = serverCustomJson;
                if (bim) {
                  let nick = serverCustomJson.bnm || '';
                  if (nick) {
                    if (msgText.indexOf('@买家老板') != -1) {
                      msgText = msgText.replace(
                        /@买家老板/g,
                        `<span class="atbuy">@买家${nick}</span>`
                      );
                    }
                    if (content.indexOf('@买家老板') != -1) {
                      content = content.replace(
                        /@买家老板/g,
                        `<span class="atbuy">@买家${nick}</span>`
                      );
                    }
                  }
                }
                if (sim) {
                  let nick = serverCustomJson.snm || '';
                  if (nick) {
                    if (msgText.indexOf('@卖家老板') != -1) {
                      msgText = msgText.replace(
                        /@卖家老板/g,
                        `<span class="atsell">@卖家${nick}</span>`
                      );
                    }
                    if (content.indexOf('@卖家老板') != -1) {
                      content = content.replace(
                        /@卖家老板/g,
                        `<span class="atsell">@卖家${nick}</span>`
                      );
                    }
                  }
                }
              }

              return compile(
                `<div className="kk_custom_msg_wrapper">
                  <div className="kk_custom_msg_title" dangerouslySetInnerHTML={{ __html: context.msgText }}></div>
                  <div className="kk_custom_msg_content"
                  dangerouslySetInnerHTML={{ __html: context.content }}></div>
                  <div className="action_box">
                    {context.actions.filter(action => context.canShow(action)).map((action, index) => (
                    <div className="kk_custom_msg_action"
                    key={index}
                    onClick={()=>{context.handleActionClick(action,context.data)}}>{action.name}</div>
                  ))}
                  </div>

              </div>`,
                { msgText, content, actions, handleActionClick, canShow, data }
              );
            } else if (
              msg.type === 'custom' &&
              msg.attach.type === 'kk_order_msg_fed'
            ) {
              const { title, content } = msg.attach.body;
              const { orderId } = msg.attach.data || {};
              // const handleActionClick = this.goOrder;
              return compile(
                `<div>
                  <div className="kk_custom_msg_wrapper">
                    <div className="kk_custom_msg_title oneline">
                      { context.title }
                    </div>
                    <div
                      dangerouslySetInnerHTML={{ __html: context.content }}
                      className="kk_custom_msg_content"
                    ></div>
                  </div>
                </div>`,
                { title, content, orderId }
              );
            } else if (
              msg.type === 'custom' &&
              msg.attach.type === 'kk_product_msg_fed'
            ) {
              const { title, content } = msg.attach.body;
              const { productId } = msg.attach.data || {};
              // const handleActionClick = this.goProduct;
              return compile(
                `<div>
                  <div className="kk_custom_msg_wrapper">
                    <div className="kk_custom_msg_title oneline">
                      { context.title }
                    </div>
                    <div
                      dangerouslySetInnerHTML={{ __html: context.content }}
                      className="kk_custom_msg_content"
                    ></div>
                  </div>
                </div>`,
                { title, content, productId }
              );
            }
          },
          // renderTeamCustomMessage: msg => {
          //   return null;
          // },
          msgOperMenu: [
            {
              key: 'recall',
              show: 1,
            },
            {
              key: 'reply',
            },
            {
              key: 'delete',
              show: 0,
            },
            {
              key: 'forward',
            },
            // {
            //   key: 'forward2',
            //   icon: compile(
            //     `<i className="iconfontnew icon-zhuanfa mg-right" />`
            //   ),
            //   label: '转发咨询卖家',
            //   show: 1,
            //   onClick: msg => {
            //     if (msg.key !== 'forward2') {
            //       const { sessionId } = msg;
            //       if (sessionId) {
            //         this.getByKeyDb(`KF_${sessionId}`).then(res => {
            //           const productSn = res ? res.productSn : '';
            //           const { body = '' } = msg;
            //           this.forward2Form.productSn = productSn;
            //           this.forward2Form.question = body;
            //           this.forward2Form.originQuestion = body;
            //           this.forward2FormDialog = true;
            //         });
            //       }
            //     }
            //   }
            // },
            {
              key: 'edit',
              icon: compile(`<i className="iconfontnew icon-jilu mg-right" />`),
              label: '添加咨询小记',
              show: 1,
              onClick: (msg) => {
                const { attach = {} } = msg;
                const { data } = attach;
                if (data) {
                  const { type, productSn } = data;
                  if (type === 'product') {
                    this.postFormRecord = {
                      consultContent: '',
                      consultTo: '',
                      consultType: 0,
                      productSn,
                    };
                    this.showRecordByKFDialog = true;
                  }
                }
              },
            },
            {
              key: 'product',
              icon: compile(
                `<i className="iconfontnew icon-chaxun mg-right" />`
              ),
              label: '查询咨询小记',
              show: 1,
              onClick: (msg) => {
                // const { sessionId } = msg;
                // if (sessionId) {
                //   this.getByKeyDb(`KF_${sessionId}`).then(res => {
                //     if (res && res.productSn) {
                //       const { productSn } = res;
                //       this.$emit('doRecordCard', productSn, 1);
                //     }
                //   });
                // }
                const { attach = {} } = msg;
                const { data } = attach;
                if (data) {
                  const { type, productSn } = data;
                  if (type == 'product') {
                    this.$emit('doRecordCard', productSn, 1);
                  }
                }
              },
            },
            {
              key: 'order',
              icon: compile(
                `<i className="iconfontnew icon-065chakandingdan mg-right" />`
              ),
              label: '查询订单信息',
              show: 1,
              onClick: (msg) => {
                const { attach = {} } = msg;
                const { data } = attach;
                if (data) {
                  const { type, orderSn } = data;
                  if (type == 'order') {
                    this.$emit('doRecordCard', orderSn, 2);
                  }
                }
              },
            },
            {
              key: 'danbao',
              icon: compile(
                `<i className="iconfontnew icon-falvbaohu mg-right" />`
              ),
              label: '担保建群',
              show: 1,
              onClick: (msg) => {
                if (
                  msg.type === 'custom' &&
                  msg.attach &&
                  msg.attach.type === 'kk_product_msg_fed'
                ) {
                  // 自定义信息可以操作
                  const { productCategoryId } = msg.attach.data || {};
                  this.doDanbao(productCategoryId);
                }
              },
            },
          ],
          onSendText: async (data) => {
            const { value = '', scene, to } = data;
            if (!value) {
              return;
            }
            const realText = value.trim();
            if (scene !== 'team' && /^[0-9a-zA-Z]+$/.test(realText)) {
              const params = {
                productSn: realText,
                ignore: 1,
              };
              const res = await detailBySn(params);
              if (res && res.code == 200 && res.data) {
                const data = res.data;
                data.product.subTitle=data.product.subTitle.substring(0,30)
                data.product.detailTitle==data.product.detailTitle.substring(0,30)
                data.product.name==data.product.name.substring(0,30)
                // return
                const attach = util.createProdcutAttach(data);
                const { store } = window.__xkit_store__;
                const myAccount = store.userStore.myUserInfo.account;
                store.msgStore
                  .sendCustomMsgActive({
                    scene: scene,
                    from: myAccount,
                    to: to,
                    attach: JSON.stringify(attach),
                  })
                  .then((res) => {
                    // 让消息滚动到可视区域
                    document.getElementById(`${res.idClient}`).scrollIntoView();
                  })
                  .catch((err) => {
                    console.log('发送失败', err);
                  });
              } else {
                this.sendTxt(data);
              }
            } else {
              this.sendTxt(data);
            }
          },
        },
        this.$refs.chat
      );
      this.$uikit.render(ContactListContainer, null, this.$refs.contactList);
      this.$uikit.render(
        ContactInfoContainer,
        {
          afterSendMsgClick: () => {
            this.model = 'chat';
          },
          onGroupItemClick: () => {
            this.model = 'chat';
          },
        },
        this.$refs.contactInfo
      );

      const { nim, store } = window.__xkit_store__;
      nim.nim.getMyInfo({
        done: (err, userInfo) => {
          this.userInfo = userInfo;
        },
      });

      nim.on('willReconnect', (obj) => {
        //cmdError, 302, The user name or password is incorrect
        // this.refreshImToken();
      });
      nim.on('disconnect', (obj) => {
        this.refreshImToken();
      });
      nim.on('msg', (msg) => {
        const { attach = {}, sessionId = '', ext } = msg;
        const { data } = attach;

        const fullPath = this.$route.fullPath;
        if (!this.isMute(msg)) {
          this.playSound();
        }
        if (fullPath == '/productKF/imList') {
          if (ext) {
            try {
              const extJson = JSON.parse(ext);
              if (extJson) {
                const { yxAitMsg } = extJson;
                if (yxAitMsg) {
                  // 如果有@
                  const to = Object.keys(yxAitMsg)[0];
                  const { store, nim } = window.__xkit_store__;
                  const account = store.userStore.myUserInfo.account;
                  if (to == account) {
                    // 有人@我
                    this.notifyModal = this.$notify.warning({
                      customClass: 'imNotify',
                      title: '您有新的消息',
                      message: msg.body,
                      duration: 0,
                      onClick: () => {
                        this.goChatByAt(msg);
                      },
                    });
                  }
                }
              }
            } catch (e) {
              console.log(e);
            }
          }
        } else {
          this.notifyModal = this.$notify.warning({
            customClass: 'imNotify',
            title: '您有新的消息',
            message: msg.body,
            duration: 0,
            onClick: () => {
              this.$router.push({
                path: '/productKF/imList',
              });
            },
          });
        }

        if (data) {
          const { type } = data;
          if (type === 'product') {
            const { productSn } = data;
            if (productSn) {
              const key = `KF_${sessionId}`;
              this.updateByKeyDb(key, {
                key,
                productSn,
              });
            }
          }
        }
        const { store } = window.__xkit_store__;
        if (
          sessionId.indexOf('team-') === 0 &&
          sessionId === store.uiStore.selectedSession
        ) {
          // 选中的群有消息
          this.throttledGetDetailForCard(sessionId);
        }
      });

      this.disposeAutorun = autorun(() => {
        // 切换到某个聊天界面
        const { store } = window.__xkit_store__;
        const selectedSession = store.uiStore.selectedSession;
        const tempList = selectedSession.split('-');
        const scene = tempList[0];
        tempList.shift();
        const to = tempList.join('-');
        this.addPaste();
        if (scene === 'team') {
          const teamInfo = window.__xkit_store__.store.teamStore.teams.get(to);
          const { serverCustom = '{}' } = teamInfo;
          const serverCustomJson = JSON.parse(serverCustom);
          const { quietIMList = [] } = serverCustomJson;
          const myAccount = store.userStore.myUserInfo.account;
          const findIt = quietIMList.find((ele) => ele == myAccount);
          if (findIt) {
            this.muteTxt = '取消免打扰';
          } else {
            this.muteTxt = '开启免打扰';
          }
          this.teamId = parseInt(to);
          this.getDetailForCard(this.teamId);
        } else if (scene === 'p2p') {
          this.$emit('p2pchange', to);
          this.stepList = [];
        }
      });
    },
    sendTxt(data) {
      const { store } = window.__xkit_store__;
      const { scene, to, value } = data;
      store.msgStore
        .sendTextMsgActive({
          scene,
          to,
          body: value,
        })
        .then((res) => {
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        });
    },
    playSound() {
      this.soundOpen && this.$refs.audio.play();
    },
    addPaste() {
      this.$nextTick(() => {
        let inputBox = document.getElementsByClassName(
          'chat-message-input-textarea'
        )[0];
        if (inputBox) {
          inputBox.removeEventListener('paste', this.onPaste);
          inputBox.addEventListener('paste', this.onPaste);
        }
      });
    },
    isMute(msg) {
      const { scene, to } = msg;
      let flag = false;
      if (scene == 'team') {
        const { store } = window.__xkit_store__;
        const team = store.teamStore.teams.get(to);
        if (team.serverCustom) {
          const serverCustomJson = JSON.parse(team.serverCustom);
          const { quietIMList = [] } = serverCustomJson;
          const myAccount = store.userStore.myUserInfo.account;
          const findIt = quietIMList.find((ele) => ele == myAccount);
          if (findIt) {
            flag = true;
          }
        }
      }
      return flag;
    },
    onPaste(event) {
      const items = (event.clipboardData || event.originalEvent.clipboardData)
        .items;
      for (let i = 0; i < items.length; i++) {
        if (
          items[i].kind === 'file' &&
          items[i].type.indexOf('image/') !== -1
        ) {
          const file = items[i].getAsFile();
          const { nim, store } = window.__xkit_store__;

          let sessionId = store.uiStore.selectedSession;
          let scene = 'team';
          if (sessionId.indexOf('team-') === 0) {
            sessionId = sessionId.replace('team-', '');
            sessionId = parseInt(sessionId);
          } else {
            scene = 'p2p';
            sessionId = sessionId.replace('p2p-', '');
          }

          store.msgStore
            .sendImageMsgActive({
              scene,
              to: sessionId,
              file,
            })
            .then((res) => {
              document.getElementById(`${res.idClient}`).scrollIntoView();
            });
          event.preventDefault();
        }
      }
    },
    doDanbao(productCategoryId) {
      this.showCreateDanbaoByKF(productCategoryId);
    },
    getDetailForCard(teamId) {
      this.getOrderDetail();
      this.flowStepDetail(teamId);
    },
    goChatByAt(msg) {
      const { store } = window.__xkit_store__;
      const { scene, to } = msg;
      if (scene == 'team') {
        const teamInfo = store.teamStore.teams.get(to);
        const { serverCustom = '{}' } = teamInfo;
        const serverCustomJson = JSON.parse(serverCustom);
        const { quietIMList = [] } = serverCustomJson;
        const myAccount = store.userStore.myUserInfo.account;
        const findIt = quietIMList.find((ele) => ele == myAccount);
        if (findIt) {
          // 切换到 免打扰 去
          this.type = 'team-mute';
        }
      }
      let sessionId = `${scene}-${to}`;

      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive(scene, to);
      }
      this.notifyModal && this.notifyModal.close();
    },
    flowStepDetail(teamId) {
      flowStepDetail({
        teamId,
      }).then((res) => {
        if (res.code == 200) {
          let data = res.data || '{}';
          data = JSON.parse(res.data);
          let subSteps = {};
          let mainSteps = {};
          Object.keys(data.mainSteps).forEach((key) => {
            if (data.mainSteps[key].current === 1) {
              mainSteps = data.mainSteps[key];
              subSteps = data.mainSteps[key].subSteps;
            }
          });
          let list = [];
          Object.keys(subSteps).forEach((key, index) => {
            let obj = subSteps[key];
            if (obj.state === 1) {
              this.stepActive = index;
            }
            list.push(obj);
          });
          this.stepList = list;
        }
      });
    },
    refreshImToken() {
      getIminfo().then(async (res) => {
        // 刷新token
        const { accid, token } = res.data;
        const { nim, store } = window.__xkit_store__;
        nim.nim.setOptions({
          token,
        });
        nim.connect();
      });
    },
    forward2() {
      this.$refs.forward2Form.validate((valid) => {
        if (valid) {
          const { nim, store } = window.__xkit_store__;
          let sessionId = store.uiStore.selectedSession;
          let scene = 'team';
          if (sessionId.indexOf('team-') === 0) {
            sessionId = sessionId.replace('team-', '');
            sessionId = parseInt(sessionId);
          } else {
            scene = 'p2p';
            sessionId = sessionId.replace('p2p-', '');
          }
          let ext = this.forward2Form;
          let txt = `【${this.forward2Form.productSn},${this.forward2Form.originQuestion}】该问题已为您咨询卖家，有结果第一时间告知您～`;
          store.msgStore
            .sendTextMsgActive({
              scene,
              to: sessionId,
              body: txt,
              ext,
            })
            .then((res) => {
              this.forward2FormDialog = false;
              document.getElementById(`${res.idClient}`).scrollIntoView();
            });
        }
      });
    },
    previewImg(event) {
      const { target } = event;
      const { dataset } = target;
      if (dataset && dataset.im) {
        const { store } = window.__xkit_store__;
        const imaccount = dataset.im;
        const sessionId = `p2p-${imaccount}`;
        if (store.sessionStore.sessions.get(sessionId)) {
          store.uiStore.selectSession(sessionId);
        } else {
          store.sessionStore.insertSessionActive('p2p', imaccount);
        }
      } else if (target.dataset && target.dataset.imgsrc) {
        this.showViewer = true;
        this.urlList = [target.dataset.imgsrc];
      } else if (target && target.className) {
        if (
          target.className.indexOf &&
          target.className.indexOf('kkimimg') != -1
        ) {
          this.showViewer = true;
          this.urlList = [target.src];
        }
      }
    },
    inputMsg(command) {
      const txt = this.dropList[command];
      const { nim, store } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      let scene = 'team';
      if (sessionId.indexOf('team-') === 0) {
        sessionId = sessionId.replace('team-', '');
        sessionId = parseInt(sessionId);
      } else {
        scene = 'p2p';
        sessionId = sessionId.replace('p2p-', '');
      }

      if (txt.indexOf('@买家老板') != -1) {
        const name = store.uiStore.getAppellation({
          account: this.bidim,
        });
        const txtMsg = txt.replace('@买家老板', '');
        let newTxt = `@${name} ${txtMsg}`;
        const selectedAtMembers = {
          account: this.bidim,
          appellation: name,
        };
        const ext = util.onAtMembersExtHandler(newTxt, [selectedAtMembers]);
        store.msgStore
          .sendTextMsgActive({
            scene,
            to: sessionId,
            body: newTxt,
            ext,
          })
          .then((res) => {
            document.getElementById(`${res.idClient}`).scrollIntoView();
          });
      } else {
        store.msgStore
          .sendTextMsgActive({
            scene,
            to: sessionId,
            body: txt,
          })
          .then((res) => {
            document.getElementById(`${res.idClient}`).scrollIntoView();
          });
      }
    },
    hideKferTransfer() {
      this.showKferTransfer = false;
    },
    canShow(action) {
      const { accid } = action;
      const { store, nim } = window.__xkit_store__;
      return accid.indexOf('kkzhw') == 0;
    },
    handleActionClick(action, data = {}) {
      const { url, msg, fedtemp } = action;
      
      if (fedtemp == 'kfer_contract_detail') {
        contractDetail(data.contractId).then((res) => {
          if (res.code == 200) {
            window.open(res.data.fddFileUrl);
          }
        });
      } else if (fedtemp == '127_kfer_contract_apply_form') {
        this.actionObj = _.cloneDeep(action);
        this.actionData = _.cloneDeep(data);
        this.hetongType=''
        this.showHetong = true;
      } else if (fedtemp == '127_kfer_transfer_apply_form') {
        this.actionObj = _.cloneDeep(action);
        this.actionData = _.cloneDeep(data);
        this.showKferTransfer = true;
      } else {
        if (url) {
          if (msg) {
            this.$confirm(msg, '二次确认', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
            }).then(() => {
              request({
                url: url,
                method: 'get',
              });
            });
          } else {
            request({
              url: url,
              method: 'get',
            }).then((res) => {
              this.getOrderDetail();
            });
          }
        }
      }
    },
  },
};
</script>
<style lang="scss">
.custom-tooltip-width{
  width: 600px !important;
  max-height: 300px !important;
  overflow: auto;
  border: none !important;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1) !important;
  background: #fff !important;
  border-radius: 12px !important;
  padding: 12px !important;
  font-size: 14px !important;
}
.custom-tooltip-text-label{
  font-size: 14px;
  color: #000;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
}
.msgBodyText .el-tag{
  height: auto!important;
  white-space: normal!important;
}
.note {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: calc(100% - 20px);
}
.soundswitch {
  margin-left: 10px;
}
.statics2Layer {
  // position: fixed;
  // right: 10px;
  // top: 60px;
  // z-index: 999;
  // background-color: #fff;
  // border: 1px solid #ccc;
  // padding: 10px;
}
.right_box_content {
  width: 500px;
  // min-height: 500px;
  // margin-top: 60px;
  // position: fixed;
  // right: 10px;
  // top: 60px;
  // z-index: 999;
  // background-color: #fff;
  // border: 1px solid #ccc;
  // padding: 10px;
}
.myDrawer {
  width: 500px;
  right: 0px;
  left: auto;
  z-index: 99 !important;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.2);
}
.myDrawer .el-drawer {
  width: 100% !important;
}
.right_box_content_top_box {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0px;
  background: rgba(#000, 0.8);
  z-index: 99;
}
.right_box_content_top_box_content {
  width: 300px;
  height: 200px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 200px;
  margin-left: 100px;
}
.todoList {
  width: auto;
  height: 800px;
  overflow: auto;
}
#CONTAINER-CC-TOOLBAR {
  width: 372px;
  min-height: 60px;
  top: 15px;
  right: 364px;
}
.imNotify {
  cursor: pointer;
  .el-icon-info {
    width: 30px;
    height: 30px;
    background-size: contain;
  }
  .el-icon-info::before {
    content: '';
  }
  .el-notification__content {
    width: 230px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    text-align: left;
  }
}
</style>
<style lang="scss" scoped>
.opt-item {
  width: 120px;
  cursor: pointer;
  padding: 0px 10px;
  background: #f6f6f6;
  border-radius: 10px;
  margin-right: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
  line-height: 14px !important;
  height: 36px !important;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  align-items: center;

  &.active {
    background: #fff4ee;
    color: #ff6716;
    border-color: #fff4ee;
  }
}

.opt-item:nth-child(8n) {
  margin-right: 0;
}
.goodsItem_pic_img_box{
  width: 80px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-family: PingFang Sc;
  border-radius: 12px;
display: none;
  top: 0px;
  left: 0px;
}
.goodsItem_pic_img{
  position: relative;
  border-radius: 12px;
}
.goodsItem_pic_img:hover  .goodsItem_pic_img_box {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  
}
.newActionCard /deep/.el-dialog__wrapper {
  background: rgba(0, 0, 0, 0.6);
}

.msgBodyText{
  max-width: 500px;
  border-radius: 0 8px 8px 8px;
    background-color: #fff;
    font-size: 14px;
    overflow: hidden;
    padding: 12px 16px;
    color: #000;
}
.msgBodyTitle{
  font-size: 14px;
  color: #000;
}
.msgBodyTime{
  padding-top: 4px;
    font-size: 12px;
    color: #999999;
    text-align: left;
}
.teamListClassActive{
  background-color: #ebf3fc;
}
.teamListClass:hover{
  background-color: #ebf3fc;
}
.teamListClass{
  padding: 12px;display: flex;align-items: center;
  .teamListImg{
    width: 36px;
    height: 36px;
    line-height: 36px;
    font-size: 18px;
    vertical-align: middle;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .teamDataTime{
    font-size: 12px;
    color: #999999;
  }
  .teamListContent{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0 6px 0 10px;
    flex: 1;
    min-width: 0;
    .name{
      font-size: 14px;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    }
    .msg{
      font-size: 12px;
    color: #999999;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex
;
    align-items: center;
    }
  }
}
/deep/ .chat-group-setting-add-icon {
  display: none;
}
/deep/ .kflist {
  .el-dialog__body {
    height: 500px;
    overflow: auto;
  }
}

/deep/ .gfmsg {
  font-size: 12px;
  margin-left: 10px;
  color: #fff;
  padding: 2px 5px;
  border-radius: 4px;
}
/deep/ .bname {
  background: #233cf8;
}
/deep/ .sname {
  background: #ea4e4e;
}
.hide {
  display: none;
}
/deep/ .lingdan {
  width: 20px;
  height: 14px;
  background: url('../../assets/images/lingdan.jpg') no-repeat;
  background-size: contain;
  position: relative;
  display: inline-block;
  top: 2px;
}
/deep/ .chat-message-list-tobottom {
  z-index: 99;
}
/deep/ .icon-zhanneixiaoxi {
  font-size: 16px;
  margin-right: 5px;
}
/deep/ .chat-message-list-item-wrap .ant-avatar {
  margin-right: 10px;
}
.steps {
  position: absolute;
  z-index: 299;
  width: 808px;
  left: 390px;
  padding: 10px 40px;
  display: flex;
  justify-content: space-around;
  background: #e8effa;
  border-radius: 0;
  margin: 0px 0;
  /deep/ .el-breadcrumb__inner {
    color: #000;
  }
  /deep/ .el-breadcrumb__inner:hover {
    color: #000;
  }
  /deep/ .el-breadcrumb__separator {
    color: #595b5f;
  }
  .stepActive {
    font-weight: 700;
  }
}

/deep/ .conversation-item {
  transition: none !important;
}
.kfbar .el-button {
  padding: 4px 4px;
  margin-left: 4px;
  font-size: 14px;
}

/deep/ .kkimimg {
  height: 200px;
  object-fit: contain;
  cursor: pointer;
}
.kehuqunzu {
  font-size: 24px;
}
// /deep/ .kkimimg:after {
//   content: '点击查看大图';
//   color: #000;
//   font-size: 16px;
//   opacity: 0;
// }
// /deep/ .kkimimg:hover::after {
//   opacity: 1;
// }
/deep/ .ant-dropdown-menu-item-icon.mg-right {
  margin-right: 5px;
}
.orderUrl {
  padding: 0 0 20px 30px;
}
/deep/ .customavatar {
  width: 36px;
  height: 36px;
  line-height: 36px;
  font-size: 18px;
  vertical-align: middle;
  background-color: rgb(83, 195, 243);
  border-radius: 18px;
}
.el-dropdown-menu {
  width: 500px;
}
.el-dropdown-menu.el-dropdown-menu--mini {
  width: 100px;
}
.el-dropdown-menu--mini
  .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
  height: 0;
}
.el-dropdown-menu .el-dropdown-menu__item {
  line-height: 24px;
  padding: 6px;
  max-width: 500px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.el-dropdown-menu__item--divided {
  margin: 0;
}
.el-dropdown-menu__item--divided:before {
  height: 0;
}
/deep/ .empty_box {
  margin: 0 auto;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-20%, -50%);
}
/deep/ .chat-message-list .chat-message-list-stranger-noti {
  display: none;
}
.kfbar {
  position: absolute;
  z-index: 90;
  bottom: 0;
  width: calc(100% - 474px);
  right: 68px;
  bottom: 85px;
  line-height: 40px;
  .bar_list2 {
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
    display: flex;
    background: #fff;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid #dde0e5;
    border-bottom: 0;
    .right_box {
      display: flex;
      justify-content: flex-start;
      margin-right: 5px;
      flex-wrap: wrap;
      .el-button {
        margin: 5px;
      }
      .clicked {
        background: #67c23a !important;
        border-color: #67c23a !important;
        color: #fff !important;
      }
    }
    padding-right: 10x;
  }
  .bar_list {
    display: flex;
    align-items: center;
    height: 50px;
    background: #fff;
    justify-content: space-between;
    border: 1px solid #dde0e5;
    .el-button {
      background: #fff !important;
      border-color: #409eff !important;
      color: #409eff !important;
    }
    .el-button:hover {
      background: #409eff !important;
      border-color: #409eff !important;
      color: #fff !important;
    }
    .left_box {
      display: flex;
    }
    .right_box {
      display: flex;
      justify-content: flex-start;
      margin-right: 5px;
      flex-wrap: wrap;
    }
  }
}
/deep/ .chat-message-list {
  margin-bottom: 104px;
}
/deep/ .imcard {
  .imline {
    border-bottom: 1px dashed #ccc;
    margin: 10px 0;
  }
  .avatarImg {
    width: 30px;
    height: 30px;
    border-radius: 30px;
  }
  .name {
    margin-left: 10px;
  }
  .imtxt {
    margin-right: 50px;
  }
  .ima {
    cursor: pointer;
    color: #409eff;
  }
}

/deep/
  .chat-message-list-item-wrap
  .chat-message-list-item-content-box
  .chat-message-list-item-body {
  max-width: 640px;
}
/deep/ .chat-person-setting-add-btn {
  display: none;
}

/deep/ .chat-person-setting-add-btn + .chat-person-setting-item-label {
  display: none;
}

/deep/ .kk_custom_msg_wrapper {
  border-radius: 0 8px 8px 8px;
  background-color: #fff;
  font-size: 14px;
  overflow: hidden;
  padding: 12px 16px;
  width: 350px;
  .at {
    color: #409eff;
  }
  .atbuy {
    color: #233cf8;
  }
  .atsell {
    color: #ea4e4e;
  }
  .kk_custom_msg_question {
    font-size: 12px;
    margin-top: 10px;
    color: #e60f0f;
  }
  .kk_custom_msg_question:hover {
    color: #b50000;
  }
  .el-tag {
    white-space: normal;
    line-height: 24px;
    height: auto;
  }
  .copy.iconfontnew {
    font-size: 12px;
    font-weight: 400;
  }
  .copy::after {
    content: '\e60d';
    color: #5cb6ff;
    margin-left: 5px;
    cursor: pointer;
    font-size: 14px;
  }
  .oneline {
    max-width: 260px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .twoLine {
    max-width: 260px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .kk_custom_msg_title {
    line-height: 24px;
    max-width: none;
    word-wrap: break-word;
  }
  .kk_custom_msg_content {
    font-size: 12px;
    line-height: 24px;
    color: #666;
    max-width: none;
    color: #666;
    word-wrap: break-word;
  }
  .kk_custom_msg_action {
    margin-top: 10px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    color: #409eff;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
  }
  .kk_custom_msg_action:hover {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}
/deep/ .kk_custom_msg_wrapper2 {
  width: 200px;
}
</style>
<style module>
.container {
  width: 1250px;
  height: 800px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
}

.search {
  width: 50%;
}

.add {
  margin-left: 20px;
  display: none;
}

.content {
  width: 100%;
  height: 740px;
  display: flex;
}

.left {
  width: 40px;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-icon {
  margin: 20px 0 25px 10px;
  border-radius: 50%;
  border: 1px solid #e8e8e8;
}

.iconfont {
  font-size: 24px!important;
}

.chat-icon,
.contact-icon {
  margin: 0 0 25px 0;
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  height: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.active {
  color: #2a6bf2;
}

.logout-icon {
  position: absolute;
  bottom: 10px;
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.icon-label {
  font-size: 12px;
  text-align: center;
}

.right {
  flex: 1;
  display: flex;
}

.right-list {
  width: 350px;
  border-right: 1px solid #e8e8e8;
}

.right-content {
  flex: 1;
}
.header {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: start;
  border-bottom: 1px solid #e8e8e8;
}
</style>

<style scoped>
.logo {
  width: 200px;
  height: 60px;
  -o-object-fit: cover;
  object-fit: contain;
  display: block;
  margin: 0 30px;
}

.logout-icon {
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  margin-left: 100px;
  margin-right: 15px;
  line-height: 1.15;
  display: none;
}
.icon-guanbi:hover {
  background: #e3e4e6;
  cursor: pointer;
  text-align: center;
}
.icon-guanbi {
  font-size: 30px;
}

.el-dropdown-link {
  cursor: pointer;
  margin-left: 10px;
}

.el-icon-arrow-down {
  font-size: 12px;
}
.kf-status-menu {
  width: 100px;
}
.flexWrap {
  flex-wrap: wrap;
}
.sort_item {
  margin-right: 20px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  margin-top: 10px;
}
</style>
