<template>
    <div
      class="spaceStart playSearch_wrap"
      style="
        width: 33.3333333%;
        flex-shrink: 0;
        padding-right: 20px;
      "
    >
      <div class="playSearch_tit">{{ item.name }}</div>
      <div class="spaceStart sxboxItem">
        <el-input
          :value="item.selectValue[0]"
          :placeholder="`最低${item.name}`"
          size="small"
          style="margin-right: 15px"
          type="tel"
          onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
          @input="(v) => change(v, 0)"
        ></el-input>
        <div style="margin-right: 15px; color: rgb(153, 153, 153)">-</div>
        <el-input
          :value="item.selectValue[1]"
          :placeholder="`最高${item.name}`"
          size="small"
          class=""
          type="tel"
          onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
          @input="(v) => change(v, 1)"
        ></el-input>
      </div>
    </div>
  </template>
  <script>
  export default {
    props: {
      item: {
        type: Object,
        default: () => {},
      },
      index: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {};
    },
    methods: {
      change(v, i) {
        const selectValue = this.item.selectValue;
        let result = JSON.parse(JSON.stringify(selectValue));
        result[i] = v;
        this.$emit('change', result);
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .flexWrap {
    flex-wrap: wrap;
  }
  .playSearch_wrap {
    // padding-top: 8px;
    align-items: baseline;
  }
  .playSearch_wrap .el-collapse-item__arrow {
    margin-left: 10px !important;
    margin-top: 11px !important;
  }
  .playSearch_item {
    cursor: pointer;
    padding: 0px 22px;
    background: #f6f6f6;
    letter-spacing: 0.8px;
    // border: 1px solid #dcdcdc;
    border-radius: 20px;
    color: rgba(0, 0, 0, 0.4);
    font-family: 'PingFang SC';
    font-size: 14px;
    margin-right: 12px;
    transition: all 0.3s;
    font-weight: 400;
    letter-spacing: 0.56px;
    // margin-top: 8px;
    margin-bottom: 24px;
    line-height: 36px !important;
    height: 36px !important;
    flex-shrink: 0;
  }
  .playSearch_item.active {
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 14px;
    background: #ff7a00;
  }
  
  .playSearch_tit {
    flex-shrink: 0;
    width: 60px;
    color: #606266;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.8px;
  }
  .sxboxItem {
    /deep/.el-input__inner {
      width: 108px;
      height: 36px;
      font-size: 14px;
      border-radius: 20px;
      border: 0.5px solid rgba(0, 0, 0, 0.1);
      background: var(--White, #fff);
      box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.05);
      letter-spacing: 0.64px;
      color: #000;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0.72px;
      text-align: center;
    }
    border-radius: 30px;
    background: #f6f6f6;
  }
  </style>
  