<template>
  <div class="kferTransfer el-reset-clazz">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="50%"
      :modal="false"
      center
      title="创建合同"
    >
      <el-form
        ref="contract"
        :label-width="isLabelWidth()"
        :model="contract"
        class="form-box"
        :rules="rules"
      >
       <div v-if="hetongType=='BILATERAL'">
        <el-form-item
        v-if="huikuanForm.transType||huikuanForm.transType == 0"
        :label="huikuanForm.transType == 0 ? '银行卡号' : '支付宝账号'"
          prop="bankNo"
        >
          <el-input
            disabled
            :value="`***${huikuanForm.bankNo.slice(-4)}`"
            placeholder="请输入银行卡号"
          />
        </el-form-item>
        <el-form-item
          v-else
          label="银行卡号或支付宝账号"
          prop="bankNo"
        >
          <el-input
            v-model="contract.bankNo"
            placeholder="请输入银行卡号或支付宝账号"
          />
        </el-form-item>
        <el-form-item label="收款人" prop="accountName">
          <el-input
            disabled
            v-model="huikuanForm.accountName"
            placeholder="请输入收款人"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="contract.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="游戏账号" prop="gameAccount">
          <el-input
            v-model="contract.gameAccount"
            placeholder="请输入游戏账号"
          />
        </el-form-item>
        <el-form-item label="汇款金额" prop="payAmount">
          <el-input v-model="contract.payAmount" placeholder="请输入合同金额" />
        </el-form-item>
        <el-form-item label="身份证姓名" prop="userIdName">
          <el-input v-model="contract.userIdName" disabled />
        </el-form-item>
        <el-form-item label="身份证号" prop="userIdNumber">
          <el-input v-model="contract.userIdNumber" disabled />
        </el-form-item>
        <el-form-item label="用户地址" prop="userAddress">
          <el-input v-model="contract.userAddress" />
        </el-form-item>
        <el-form-item label="图片">
          <el-image
            class="userImg"
            :src="contract.attachment.userIdPic1"
            :preview-src-list="[contract.attachment.userIdPic1]"
          ></el-image>
          <el-image
            class="userImg"
            :src="contract.attachment.userIdPic2"
            :preview-src-list="[contract.attachment.userIdPic2]"
          ></el-image>
          <el-image
            class="userImg"
            :src="contract.attachment.userOrderPic"
            :preview-src-list="[contract.attachment.userOrderPic]"
          ></el-image>
        </el-form-item>
       </div>
       <div v-if="hetongType=='TRIPARTITE'">
        <el-form-item label="游戏账号" prop="gameAccount">
          <el-input
            v-model="contract.gameAccount"
            placeholder="请输入游戏账号"
          />
        </el-form-item>
        <el-form-item label="号价" prop="productPrice">
          <el-input
            v-model="contract.productPrice"
            placeholder="请输入号价"
          />
        </el-form-item>
        <el-form-item label="服务费" prop="serviceFee">
          <el-input
            
            v-model="contract.serviceFee"
            placeholder="请输入服务费"
          />
        </el-form-item>
        <el-form-item label="是否包赔" prop="buyCompensation">
          <!-- <el-input
            
            v-model="contract.buyCompensation"
            placeholder="请输入是否包赔"
          /> -->
          <el-select style="width:100%" v-model="contract.buyCompensation">
          <el-option
            v-for="item in buyCompensationList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        </el-form-item>
        <el-form-item label="包赔类型" prop="compensationType">
          <el-select style="width:100%" v-model="contract.compensationType">
          <el-option
            v-for="item in compensationTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
          <!-- <el-input
            
            v-model="contract.compensationType"
            placeholder="请输入包赔类型"
          /> -->
        </el-form-item>
        <el-form-item label="包赔费" prop="compensationFee">
          <el-input
            
            v-model="contract.compensationFee"
            placeholder="请输入包赔费"
          />
        </el-form-item>
        <el-form-item label="总费用(服务费+包赔费)" prop="totalFee">
          <el-input
            
            v-model="contract.totalFee"
            placeholder="请输入总费用"
          />
        </el-form-item>
        <el-form-item label="账号来源" prop="gameAccountSource">
          <el-input
            
            v-model="contract.gameAccountSource"
            placeholder="请输入账号来源"
          />
        </el-form-item>
        <el-form-item label="赔付比例" prop="compensationRate">
          <el-input
            
            v-model="contract.compensationRate"
            placeholder="请输入赔付比例"
          />
        </el-form-item>
        <el-form-item label="合同类型" prop="type">
          <el-input
            disabled
            v-model="contract.type"
            placeholder="请输入合同类型"
          />
        </el-form-item>
        <el-form-item label="用户手机号" prop="buyerPhone">
          <el-input
            v-model="contract.buyerPhone"
            placeholder="请输入用户手机号"
          />
        </el-form-item>
        <el-form-item label="买家身份证姓名" prop="buyerUserIdName">
          <el-input
            disabled
            v-model="contract.buyerUserIdName"
            placeholder="请输入买家身份证姓名"
          />
        </el-form-item>
        <el-form-item label="买家身份证号码" prop="buyerUserIdNumber">
          <el-input
            disabled
            v-model="contract.buyerUserIdNumber"
            placeholder="请输入买家身份证号码"
          />
        </el-form-item>
       </div>
      </el-form>
      <div class="m-footer spaceEnd">
        <el-button @click="onCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit('contract')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// attachment: '{"userIdPic1":"","userIdPic2":"","userOrderPic":"https://images2.kkzhw.com/mall/images/********/r464yf_1722108729187.jpg"}';
// memberId: 68;
// orderId: 112257;
// orderSn: '*****************';
// payAmount: -8;
// phone: '***********';
// productCategoryName: '剑灵怀旧服';
// productSn: 'JLHJ04227109';
// subject: '游戏账号交易合同';
// userAddress: '123';
// userIdName: '陈李平';
// userIdNumber: '320481198510032813';
// username: '***********';
import { queryBankCode } from '@/api/product';
import { contractPreview, contractCreate, startSignTask } from '@/api/kf';
const defaultValue = {
  bankNo: '',
  accountName: '',
  price: '',
  hetongType:'',
};
export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {};
      }
    },
    actionObj: {
      type: Object,
      default() {
        return {};
      }
    },
    actionData: {
      type: Object,
      default() {
        return {};
      }
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: ''
    },
    hetongTypeProp: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      buyCompensationList:[
        {
          label:'购买',
          value:'购买'
        },
        {
          label:'不购买',
          value:'不购买'
        },
      ],
      compensationTypeList:[
        {
          label:'人脸包赔',
          value:'人脸包赔'
        },
        {
          label:'普通包赔',
          value:'普通包赔'
        },
        {
          label:'双倍包赔',
          value:'双倍包赔'
        },{
          label:'三倍包赔',
          value:'三倍包赔'
        },
      ],
      formLabelWidth: '100px',
      formLabelWidth1:'160px',
      formLabelWidth2:'170px',
      huikuanForm: Object.assign({}, defaultValue),
      errorTxt: '',
      rules: {
        gameAccount: [
          { required: true, message: '请输入游戏账号', trigger: 'blur' }
        ],
        productPrice:[
          { required: true, message: '请输入号价', trigger: 'blur' }
        ],
        serviceFee:[
          { required: true, message: '请输入服务费', trigger: 'blur' }
        ],
        buyCompensation:[
          { required: true, message: '请选择是否包赔', trigger: 'change' }
        ],
        compensationFee:[
          { required: true, message: '请输入包赔费', trigger: 'blur' }
        ],
        totalFee:[
          { required: true, message: '请输入总费用', trigger: 'blur' }
        ],
        gameAccountSource:[
          { required: true, message: '请输入账号来源', trigger: 'blur' }
        ],
        compensationRate:[
          { required: true, message: '请输入赔付比例', trigger: 'blur' }
        ],
        buyerPhone:[
          { required: true, message: '请输入用户手机号', trigger: 'blur' }
        ],
        payAmount: [{ required: true, message: '请输入金额', trigger: 'blur' }]
      },
      bankList: [],
      contract: {
        attachment: {}
      }
    };
  },
  mounted() {
    const values = this.actionData;
    this.huikuanForm.accountName = values.transName;
    this.huikuanForm.bankNo = values.transCard;
    this.huikuanForm.transType = values.transType;
    this.init();
    this.getContractPreview();
  },
  methods: {
    isLabelWidth(){
      if(this.hetongType=='BILATERAL'){
        return (this.huikuanForm.transType||this.huikuanForm.transType == 0)?this.formLabelWidth:this.formLabelWidth1
      }else{
        return this.formLabelWidth2
      }
      
    },
    getContractPreview() {
      let data = {
        orderId: this.orderDetail.id,
       
      };
      console.log(this.hetongTypeProp,111111);
      
      if(this.hetongTypeProp){
        data.type=this.hetongTypeProp
      }
      contractPreview(data).then(res => {
        if (res.code == 200) {
          this.contract = res.data;
          this.hetongType=res.data.type
          if (res.data.attachment) {
            
            this.contract.attachment = JSON.parse(this.contract.attachment);
            if(!this.huikuanForm.accountName){
              this.huikuanForm.accountName=res.data.userIdName
            }
            if(!this.huikuanForm.bankNo){
              this.huikuanForm.bankNo=res.data.phone
            }
          }
        }
      });
    },
    init() {
      queryBankCode().then(res => {
        const { data = '[]' } = res;
        this.bankList = JSON.parse(data);
      });
    },
    onCancel() {
      this.huikuanForm = Object.assign({}, defaultValue);
      this.$emit('close');
    },
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let data = Object.assign({}, this.contract);
          data.attachment = JSON.stringify(data.attachment);
          data.bankNo = this.huikuanForm.bankNo;
          data.accountName = this.huikuanForm.accountName;
          data.type=this.hetongType
          contractCreate(data).then(res => {
            if (res.code == 200) {
              this.$message.success('合同创建成功');
              this.onCancel();
            }
          });
        } else {
          return;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.userImg {
  width: 100px;
  height: 100px;
}
</style>
