import request from '@/utils/request';

// 答题分类相关API
export function fetchCategoryList(params) {
  return request({
    url: '/quiz/question/list',
    method: 'get',
    params: params
  });
}

export function createCategory(data) {
  return request({
    url: '/answer/category/create',
    method: 'post',
    data: data
  });
}

export function updateCategory(id, data) {
  return request({
    url: '/answer/category/update/' + id,
    method: 'post',
    data: data
  });
}

export function deleteCategory(id) {
  return request({
    url: '/answer/category/delete/' + id,
    method: 'post'
  });
}

export function getCategoryDetail(id) {
  return request({
    url: '/answer/category/' + id,
    method: 'get'
  });
}

// 题目相关API
export function fetchQuestionList(params) {
  return request({
    url: '/answer/question/list',
    method: 'get',
    params: params
  });
}

export function createQuestion(data) {
  return request({
    url: '/answer/question/create',
    method: 'post',
    data: data
  });
}

export function updateQuestion(id, data) {
  return request({
    url: '/answer/question/update/' + id,
    method: 'post',
    data: data
  });
}

export function deleteQuestion(id) {
  return request({
    url: '/answer/question/delete/' + id,
    method: 'post'
  });
}

export function getQuestionDetail(id) {
  return request({
    url: '/answer/question/' + id,
    method: 'get'
  });
}
