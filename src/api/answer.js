import request from '@/utils/request';

// 题目相关API
export function fetchQuestionList(params) {
  return request({
    url: '/quiz/question/list',
    method: 'get',
    params: params
  });
}

export function createQuestion(data) {
  return request({
    url: '/quiz/question/create',
    method: 'post',
    data: data
  });
}

export function updateQuestion(id, data) {
  return request({
    url: '/quiz/question/update/' + id,
    method: 'post',
    data: data
  });
}

export function deleteQuestion(id) {
  return request({
    url: '/quiz/question/delete/' + id,
    method: 'post'
  });
}

export function updateQuestionStatus(id, status) {
  return request({
    url: '/quiz/question/update/status/' + id,
    method: 'post',
    params: { status: status }
  });
}

export function getQuestionDetail(id) {
  return request({
    url: '/quiz/question/' + id,
    method: 'get'
  });
}
