import request from '@/utils/request';

const webkk = process.env.BASE_API_FED;

export function memberManageCreate2(data) {
  return request({
    url: '/memberManage/create2',
    method: 'post',
    data: data
  });
}

export function memberManageUpdate2(id, data) {
  return request({
    url: '/memberManage/update/' + id,
    method: 'post',
    data: data
  });
}

export function getMemberDetail(id) {
  return request({
    url: '/memberManage/detail/' + id,
    method: 'get',
    noLoading: 1
  });
}

export function memberManageList(params) {
  return request({
    url: '/memberManage/list',
    method: 'get',
    params
  });
}

export function updatePassword(data, params) {
  return request({
    url: '/memberManage/updatePassword',
    method: 'post',
    data,
    params
  });
}
export function updatePassword2(data, params) {
  return request({
    url: '/memberManage/updatePassword2',
    method: 'post',
    data,
    params
  });
}

export function memberManageUpdateSimple(id, data) {
  return request({
    url: '/memberManage/updateSimple/' + id,
    method: 'post',
    data: data
  });
}

export function memberManageDelete(id) {
  return request({
    url: '/memberManage/delete/' + id,
    method: 'get'
  });
}

export function createDanbaoByKF(params) {
  return request({
    url: webkk + '/kkim/team/createDanbaoByKF',
    method: 'post',
    params
  });
}

export function memberImInit(params) {
  return request({
    url: webkk + '/order/flowTools/memberImInit',
    method: 'get',
    params
  });
}
