import request from '@/utils/request';
const webkk = process.env.BASE_API_FED;
export function login(username, password) {
  return request({
    url: '/admin/login',
    method: 'post',
    data: {
      username,
      password
    }
  });
}

export function LoginCustom(params) {
  return request({
    url: webkk + '/sso/login',
    method: 'post',
    params
  });
}

export function protalLogin(params) {
  return request({
    url: '/admin/portalLogin',
    method: 'post',
    params
  });
}

export function getInfo() {
  return request({
    url: '/admin/info',
    method: 'get'
  });
}

export function logout() {
  return request({
    url: '/admin/logout',
    method: 'post'
  });
}

export function fetchList(params) {
  return request({
    url: '/admin/list',
    method: 'get',
    params: params
  });
}

export function createAdmin(data) {
  return request({
    url: '/admin/register',
    method: 'post',
    data: data
  });
}

export function updateAdmin(id, data) {
  return request({
    url: '/admin/update/' + id,
    method: 'post',
    data: data
  });
}

export function updateStatus(id, params) {
  return request({
    url: '/admin/updateStatus/' + id,
    method: 'post',
    params: params
  });
}

export function deleteAdmin(id) {
  return request({
    url: '/admin/delete/' + id,
    method: 'post'
  });
}

export function getRoleByAdmin(id) {
  return request({
    url: '/admin/role/' + id,
    method: 'get'
  });
}

export function allocRole(data) {
  return request({
    url: '/admin/role/update',
    method: 'post',
    data: data
  });
}

export function initIM(params) {
  return request({
    url: '/admin/initIM',
    method: 'get',
    params
  });
}

export function sendPhoneCode(params) {
  return request({
    url:webkk +  '/sso/getAuthCode',
    method: 'get',
    params
  });
}

export function updateAdminPassword(data) {
  return request({
    url: '/admin/updatePassword',
    method: 'post',
    data,
  });
}
