import request from '@/utils/request';
import util from '@/utils/index';

export function getConfigList(params) {
  return request({
    url: '/config/list',
    method: 'get',
    params: params
  });
}

export function createConfig(data) {
  return request({
    url: '/config/create',
    method: 'post',
    data: data
  });
}

export function updateConfig(id,data) {
    return request({
      url: `/config/update/${id}`,
      method: 'post',
      data: data
    });
  }
  
  export function deleteConfig(id) {
    return request({
      url: `/config/delete/${id}`,
      method: 'post',
    });
  }
  
