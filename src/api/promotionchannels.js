import request from '@/utils/request';
import util from '@/utils/index';

export function getPromotionChannelsList(params) {
  return request({
    url: '/promotionChannels/list',
    method: 'get',
    params: params
  });
}

export function createPromotionChannels(data) {
  return request({
    url: '/promotionChannels/create',
    method: 'post',
    data: data
  });
}

export function updatePromotionChannels(id,data) {
    return request({
      url: `/promotionChannels/update/${id}`,
      method: 'post',
      data: data
    });
  }
  
  export function deletePromotionChannels(params) {
    return request({
      url: `/promotionChannels/delete`,
      method: 'get',
      params
    });
  }
  
