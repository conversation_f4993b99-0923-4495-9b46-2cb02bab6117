import request from '@/utils/request';

const webkk = process.env.BASE_API_FED;
const webkkSearch =process.env.BASE_API_SEARCH

// export function getProductAttribute(id) {
//     return request({
//         url: webkk + '/home/<USER>/attrInfo2/' + id,
//         method: 'get',
//     });
// }
export function getProductAttribute(id) {
    return request({
        url: webkk + '/home/<USER>/attrInfo/' + id,
        method: 'get',
    });
}
export function getSearchTagList(id,params) {
    return request({
        url: '/searchTag/list/' + id,
        method: 'get',
        params
    });
}

export function addSearchTag(data) {
    return request({
        url: '/searchTag/create',
        method: 'post',
        data
    });
}

export function updateSearchTag(id,data) {
    return request({
        url: '/searchTag/update/' + id,
        method: 'post',
        data
    });
}
export function deleteSearchTag(id) {
    return request({
        url: '/searchTag/delete/' + id,
        method: 'get',
    });
}
export function detailSearchTag(id) {
    return request({
        url: '/searchTag/detail/' + id,
        method: 'get',
    });
}

export function searchProductList2(params, data) {
    return request({
      url: webkkSearch+'/kkSearch/innerSearch',
      method: 'post',
      data,
      params,
    });
  }


  export function getSearchZoneList(id) {
    return request({
        url: '/searchZone/list/' + id,
        method: 'get',
    });
}
export function addSearchZone(data) {
    return request({
        url: '/searchZone/create',
        method: 'post',
        data
    });
}

export function updateSearchZone(id,data) {
    return request({
        url: '/searchZone/update/' + id,
        method: 'post',
        data
    });
}

export function detailSearchZone(id) {
    return request({
        url: '/searchZone/detail/' + id,
        method: 'get',
    });
}


export function deleteSearchZone(id) {
    return request({
        url: '/searchZone/delete/' + id,
        method: 'get',
    });
}




export function getListByZone(id) {
    return request({
        url: '/searchZoneTag/listByZone/' + id,
        method: 'get',
    });
}
export function addSearchZoneTag(data) {
    return request({
        url: '/searchZoneTag/create',
        method: 'post',
        data
    });
}
export function deleteSearchZoneTag(id) {
    return request({
        url: '/searchZoneTag/delete/' + id,
        method: 'get',
    });
}
export function updateSearchZoneTag(id,data) {
    return request({
        url: '/searchZoneTag/update/' + id,
        method: 'post',
        data
    });
}

export function detailSearchZoneTag(id) {
    return request({
        url: '/searchZoneTag/detail/' + id,
        method: 'get',
    });
}